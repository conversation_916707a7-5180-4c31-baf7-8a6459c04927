<template>
  <div ref="playerContainer" class="plyr-video-container">
    <video ref="videoElement" :src="src" :poster="poster" preload="metadata" crossorigin="anonymous" playsinline
      @loadedmetadata="handleLoadedMetadata" @error="handleError">
      <source :src="src" :type="videoType" />
      您的浏览器不支持视频播放
    </video>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed, readonly } from 'vue'
import Plyr from 'plyr'
import 'plyr/dist/plyr.css'

interface Props {
  src: string
  poster?: string
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
  controls?: boolean
  volume?: number
  speed?: number[]
  quality?: { default: number; options: number[] }
  captions?: { active: boolean; language: string; label: string }[]
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: false,
  muted: false,
  loop: false,
  controls: true,
  volume: 1,
  speed: () => [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
})

interface Emits {
  ready: [player: Plyr]
  play: []
  pause: []
  ended: []
  timeupdate: [currentTime: number]
  volumechange: [volume: number]
  error: [error: any]
  loadedmetadata: [dimensions: { width: number; height: number }]
}

const emit = defineEmits<Emits>()

const playerContainer = ref<HTMLElement>()
const videoElement = ref<HTMLVideoElement>()
let player: Plyr | null = null

// 获取视频MIME类型
const videoType = computed(() => {
  const url = props.src.toLowerCase()
  if (url.includes('.mp4')) return 'video/mp4'
  if (url.includes('.webm')) return 'video/webm'
  if (url.includes('.ogg') || url.includes('.ogv')) return 'video/ogg'
  if (url.includes('.avi')) return 'video/x-msvideo'
  if (url.includes('.mov')) return 'video/quicktime'
  return 'video/mp4' // 默认
})

// 初始化播放器
const initPlayer = async () => {
  if (!videoElement.value) return

  await nextTick()

  // Plyr配置
  const options = {
    controls: props.controls ? [
      'play-large',
      'play',
      'progress',
      'current-time',
      'duration',
      'mute',
      'volume',
      'settings',
      'fullscreen'
    ] : [],
    settings: ['captions', 'quality', 'speed'],
    speed: { selected: 1, options: props.speed },
    volume: props.volume,
    muted: props.muted,
    autoplay: props.autoplay,
    loop: { active: props.loop },
    hideControls: true,
    clickToPlay: true,
    keyboard: { focused: true, global: false },
    tooltips: { controls: true, seek: true },
    captions: { active: false, language: 'auto', update: false },
    fullscreen: { enabled: true, fallback: true, iosNative: false },
    ratio: null, // 自动比例
    storage: { enabled: true, key: 'plyr' },
    quality: {
      default: 720,
      options: [4320, 2880, 2160, 1440, 1080, 720, 576, 480, 360, 240],
      forced: true,
      onChange: (quality: number) => {
        console.log('质量变更:', quality)
      }
    }
  }

  // 创建播放器实例
  player = new Plyr(videoElement.value, options)

  // 绑定事件
  bindEvents()

  emit('ready', player)
}

// 绑定播放器事件
const bindEvents = () => {
  if (!player) return

  player.on('ready', () => {
    console.log('Plyr播放器就绪')
  })

  player.on('play', () => {
    emit('play')
  })

  player.on('pause', () => {
    emit('pause')
  })

  player.on('ended', () => {
    emit('ended')
  })

  player.on('timeupdate', () => {
    emit('timeupdate', player!.currentTime)
  })

  player.on('volumechange', () => {
    emit('volumechange', player!.volume)
  })

  player.on('error', (event) => {
    console.error('Plyr播放器错误:', event)
    emit('error', event)
  })
}

// 处理视频元数据加载
const handleLoadedMetadata = () => {
  if (videoElement.value) {
    emit('loadedmetadata', {
      width: videoElement.value.videoWidth,
      height: videoElement.value.videoHeight
    })
  }
}

// 处理播放错误
const handleError = (error: any) => {
  console.error('视频加载错误:', error)
  emit('error', error)
}

// 销毁播放器
const destroyPlayer = () => {
  if (player) {
    player.destroy()
    player = null
  }
}

// 监听src变化，重新初始化播放器
watch(() => props.src, async (newSrc) => {
  if (newSrc && videoElement.value) {
    destroyPlayer()
    await nextTick()
    initPlayer()
  }
})

// 暴露播放器实例和方法
defineExpose({
  player: readonly(ref(player)),
  play: () => player?.play(),
  pause: () => player?.pause(),
  stop: () => player?.stop(),
  restart: () => player?.restart(),
  seek: (time: number) => player && (player.currentTime = time),
  setVolume: (volume: number) => player && (player.volume = volume),
  mute: () => player && (player.muted = true),
  unmute: () => player && (player.muted = false),
  enterFullscreen: () => player?.fullscreen.enter(),
  exitFullscreen: () => player?.fullscreen.exit(),
  destroy: destroyPlayer
})

onMounted(() => {
  initPlayer()
})

onUnmounted(() => {
  destroyPlayer()
})
</script>

<style scoped>
.plyr-video-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Plyr自定义样式 */
:deep(.plyr) {
  width: 100%;
  height: 100%;
}

:deep(.plyr__video-wrapper) {
  background: #000;
}

:deep(.plyr__controls) {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
}

:deep(.plyr__control--overlaid) {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

:deep(.plyr__control--overlaid:hover) {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  color: white;
  transform: scale(1.1);
}

:deep(.plyr__progress__buffer) {
  background: rgba(255, 255, 255, 0.25);
}

:deep(.plyr__progress__played) {
  background: #00d4aa;
}

:deep(.plyr__volume__display) {
  color: white;
}

:deep(.plyr__time) {
  color: rgba(255, 255, 255, 0.9);
}

/* 暗色主题适配 */
.dark :deep(.plyr__control--overlaid) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.8);
}

.dark :deep(.plyr__control--overlaid:hover) {
  background: rgba(255, 255, 255, 0.2);
  border-color: white;
}
</style>

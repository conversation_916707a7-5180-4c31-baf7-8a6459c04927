{"name": "knowledge", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@iconify-json/heroicons": "^1.1.0", "@iconify-json/lucide": "^1.1.0", "@tanstack/vue-query": "^5.83.0", "@unocss/vite": "^0.62.0", "@videojs/themes": "^1.0.1", "@vueuse/core": "^10.11.0", "ant-design-vue": "^4.2.6", "dayjs": "^1.11.10", "dexie": "^3.2.7", "highlight.js": "^11.9.0", "ky": "^1.8.2", "markdown-it": "^14.0.0", "markdown-it-abbr": "^2.0.0", "markdown-it-container": "^4.0.0", "markdown-it-deflist": "^3.0.0", "markdown-it-footnote": "^4.0.0", "markdown-it-ins": "^4.0.0", "markdown-it-mark": "^4.0.0", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "md-editor-v3": "^5.8.2", "p-limit": "^6.2.0", "pako": "^2.1.0", "photoswipe": "^5.4.4", "pinia": "^2.2.0", "plyr": "^3.7.8", "unocss": "^0.62.0", "vditor": "^3.11.1", "video.js": "^8.23.3", "vue": "^3.4.0", "vue-advanced-cropper": "^2.8.9", "vue-router": "^4.4.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node18": "^18.2.4", "@types/lodash-es": "^4.17.12", "@types/node": "^18.19.0", "@types/pako": "^2.0.3", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.28.0", "npm-run-all2": "^6.2.0", "prettier": "^3.3.0", "typescript": "~5.4.0", "vite": "^5.4.0", "vite-plugin-singlefile": "^2.3.0", "vite-plugin-vue-devtools": "^7.3.0", "vue-tsc": "^2.0.0"}}
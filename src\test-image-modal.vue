<template>
  <div class="min-h-screen bg-gray-100 dark:bg-gray-900 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-6 text-gray-900 dark:text-gray-100">图片详情模态窗口测试</h1>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">功能特性</h2>
        <ul class="space-y-2 mb-6 text-gray-600 dark:text-gray-400">
          <li>✅ 移除了顶部标题栏，提供更沉浸的体验</li>
          <li>✅ 关闭按钮移至页面右上角，浮动显示</li>
          <li>✅ 添加了浅灰色半透明背景蒙版</li>
          <li>✅ 优化了图片显示逻辑，智能填充可用空间</li>
          <li>✅ 保持图片原始宽高比，不会变形</li>
          <li>✅ 完整支持暗黑模式切换</li>
          <li>✅ 响应式设计，适配移动设备</li>
        </ul>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <button @click="openModal(testImages.landscape)"
            class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
            🖼️ 横向图片 (1920x1080)
          </button>

          <button @click="openModal(testImages.portrait)"
            class="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium">
            🖼️ 纵向图片 (1080x1920)
          </button>

          <button @click="openModal(testImages.square)"
            class="px-6 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors font-medium">
            🖼️ 正方形图片 (1080x1080)
          </button>

          <button @click="toggleDarkMode"
            class="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors font-medium">
            🌙 切换暗黑模式
          </button>
        </div>
      </div>
    </div>

    <ImageDetailModal v-if="showModal" :image="testImage" @close="showModal = false" @edit="handleEdit"
      @delete="handleDelete" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ImageDetailModal from './components/image/ImageDetailModal.vue'

const showModal = ref(false)
const testImage = ref({})

// 暗黑模式切换
const toggleDarkMode = () => {
  document.documentElement.classList.toggle('dark')
}

// 不同尺寸的测试图片数据
const testImages = {
  landscape: {
    id: 'test-landscape',
    originalName: 'landscape-1920x1080.jpg',
    size: 1024000, // 1MB
    width: 1920,
    height: 1080,
    type: 'image/jpeg',
    uploadTime: new Date(),
    tags: ['风景', '横向', '测试'],
    urls: [
      {
        id: 'url-1',
        hostId: 'host-1',
        hostName: 'Picsum Photos',
        url: 'https://picsum.photos/1920/1080?random=1',
        uploadTime: new Date(),
        status: 'active' as const
      }
    ]
  },
  portrait: {
    id: 'test-portrait',
    originalName: 'portrait-1080x1920.jpg',
    size: 1536000, // 1.5MB
    width: 1080,
    height: 1920,
    type: 'image/jpeg',
    uploadTime: new Date(),
    tags: ['人像', '纵向', '测试'],
    urls: [
      {
        id: 'url-2',
        hostId: 'host-1',
        hostName: 'Picsum Photos',
        url: 'https://picsum.photos/1080/1920?random=2',
        uploadTime: new Date(),
        status: 'active' as const
      }
    ]
  },
  square: {
    id: 'test-square',
    originalName: 'square-1080x1080.jpg',
    size: 1200000, // 1.2MB
    width: 1080,
    height: 1080,
    type: 'image/jpeg',
    uploadTime: new Date(),
    tags: ['正方形', '艺术', '测试'],
    urls: [
      {
        id: 'url-3',
        hostId: 'host-1',
        hostName: 'Picsum Photos',
        url: 'https://picsum.photos/1080/1080?random=3',
        uploadTime: new Date(),
        status: 'active' as const
      }
    ]
  }
}

// 打开模态窗口
const openModal = (imageData: any) => {
  testImage.value = imageData
  showModal.value = true
}

const handleEdit = (image: any) => {
  console.log('编辑图片:', image)
  alert('编辑功能已触发，请查看控制台')
}

const handleDelete = (imageId: string) => {
  console.log('删除图片:', imageId)
  if (confirm('确定要删除这张图片吗？')) {
    showModal.value = false
    alert('图片已删除')
  }
}
</script>

<template>
  <Teleport to="body">
    <!-- 背景蒙版 -->
    <Transition enter-active-class="transition-opacity duration-300" enter-from-class="opacity-0"
      enter-to-class="opacity-100" leave-active-class="transition-opacity duration-300" leave-from-class="opacity-100"
      leave-to-class="opacity-0">
      <div v-if="visible" class="fixed inset-0 z-40 backdrop-blur-sm" style="background-color: rgba(0, 0, 0, 0.85);"
        @click="handleBackdropClick" @keydown.esc="close" tabindex="-1">
      </div>
    </Transition>

    <!-- 关闭按钮 - 固定在页面右上角 -->
    <Transition enter-active-class="transition-all duration-300" enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100" leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
      <button v-if="visible" @click="close"
        class="fixed top-24 right-4 z-60 p-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white hover:text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110">
        <div class="i-heroicons-x-mark w-6 h-6"></div>
      </button>
    </Transition>

    <!-- 模态窗口内容 - Instagram风格 -->
    <Transition enter-active-class="transition-all duration-300 ease-out" enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100" leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
      <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center pt-20 pb-10 px-5"
        @click="handleBackdropClick">
        <div class="instagram-modal bg-white dark:bg-gray-900 shadow-2xl overflow-hidden flex"
          :style="instagramModalStyle" @click.stop>

          <!-- 左侧：媒体预览区域 - Instagram风格 -->
          <div class="instagram-image-container bg-black flex items-center justify-center relative"
            :style="imageContainerStyle">
            <div ref="imageContainer" class="relative w-full h-full flex items-center justify-center"
              :class="{ 'cursor-move': !isVideo }" @mousedown="!isVideo && startDrag" @wheel="!isVideo && handleWheel">

              <!-- Video.js智能播放器 -->
              <VideoJsPlayer v-if="isVideo" :src="getPrimaryImageUrl(image)" class="w-full h-full"
                @loadedmetadata="handleVideoLoad" @error="handleVideoError" @ready="handlePlayerReady" />

              <!-- 图片显示 -->
              <img v-else ref="imageElement" :src="getPrimaryImageUrl(image)" :alt="image.originalName"
                class="instagram-image object-contain w-full h-full transition-transform duration-200"
                :style="{ transform: `scale(${scale}) translate(${translateX}px, ${translateY}px)` }"
                @load="handleImageLoad" />
            </div>

            <!-- 缩放控制 - 仅图片模式显示 -->
            <div v-if="!isVideo"
              class="absolute bottom-4 left-4 flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-lg p-2 shadow-sm">
              <button @click="zoomOut" :disabled="scale <= 0.1"
                class="p-1 text-white hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                <div class="i-heroicons-minus w-4 h-4"></div>
              </button>
              <span class="text-sm text-white min-w-12 text-center px-2">
                {{ Math.round(scale * 100) }}%
              </span>
              <button @click="zoomIn" :disabled="scale >= 5"
                class="p-1 text-white hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                <div class="i-heroicons-plus w-4 h-4"></div>
              </button>
              <button @click="resetZoom" class="p-1 text-white hover:text-gray-300 transition-colors">
                <div class="i-heroicons-arrow-path w-4 h-4"></div>
              </button>
            </div>

            <!-- 图片信息悬浮显示 -->
            <div class="absolute top-4 left-4 bg-black/50 backdrop-blur-sm rounded-lg p-2 shadow-sm">
              <div class="text-xs text-white">
                <div v-if="(image.width && image.height) || (actualImageSize.width && actualImageSize.height)">
                  {{ (image.width || actualImageSize.width) }} × {{ (image.height || actualImageSize.height) }}
                </div>
                <div>{{ formatFileSize(image.size) }}</div>
              </div>
            </div>
          </div>

          <!-- 右侧：信息面板 - 黄金比例 38.2% -->
          <div class="info-panel flex flex-col bg-white dark:bg-gray-800">
            <!-- 图片信息区域 -->
            <div class="flex-1 overflow-y-auto">
              <div class="info-content p-4">


                <!-- 基本信息卡片 -->
                <a-card size="small" title="基本信息" class="info-card">
                  <div class="space-y-3">
                    <!-- 文件名编辑 -->
                    <div>
                      <label class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 block">文件名</label>
                      <div v-if="!isEditingName" class="flex items-center group">
                        <span class="text-sm text-gray-900 dark:text-gray-100 break-all flex-1">{{ image.originalName }}</span>
                        <a-button @click="startEditingName" type="text" size="small"
                          class="opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                          <template #icon>
                            <div class="i-heroicons-pencil w-4 h-4"></div>
                          </template>
                        </a-button>
                      </div>
                      <div v-else class="flex items-center space-x-2">
                        <a-input ref="nameInput" v-model:value="editingName" size="small" @keyup.enter="saveNameEdit"
                          @keyup.esc="cancelNameEdit" @blur="saveNameEdit" />
                        <a-button @click="saveNameEdit" type="text" size="small">
                          <template #icon>
                            <div class="i-heroicons-check w-4 h-4 text-green-600"></div>
                          </template>
                        </a-button>
                        <a-button @click="cancelNameEdit" type="text" size="small">
                          <template #icon>
                            <div class="i-heroicons-x-mark w-4 h-4"></div>
                          </template>
                        </a-button>
                      </div>
                    </div>

                    <!-- 其他基本信息 -->
                    <div class="grid grid-cols-1 gap-3">
                      <div>
                        <label class="text-sm font-medium text-gray-500 dark:text-gray-400">上传时间</label>
                        <p class="text-sm text-gray-900 dark:text-gray-100">{{ formatDate(image.uploadTime) }}</p>
                      </div>
                      <div v-if="(image.width && image.height) || (actualImageSize.width && actualImageSize.height)">
                        <label class="text-sm font-medium text-gray-500 dark:text-gray-400">
                          {{ isVideo ? '视频尺寸' : '图片尺寸' }}
                        </label>
                        <p class="text-sm text-gray-900 dark:text-gray-100">
                          {{ (image.width || actualImageSize.width) }} × {{ (image.height || actualImageSize.height) }}
                        </p>
                      </div>
                    </div>
                  </div>
                </a-card>

                <!-- 备份信息卡片 -->
                <a-card size="small" class="backup-card">
                  <template #title>
                    <div class="flex items-center justify-between">
                      <span>备份信息</span>
                      <div class="flex items-center space-x-1">
                        <a-tooltip title="添加URL">
                          <a-button @click="showAddUrlModal = true" type="text" size="small">
                            <template #icon>
                              <div class="i-heroicons-plus w-4 h-4"></div>
                            </template>
                          </a-button>
                        </a-tooltip>
                        <a-tooltip title="上传到随机图床">
                          <a-button @click="uploadToRandomHost" type="text" size="small" :loading="isUploading">
                            <template #icon>
                              <div class="i-heroicons-cloud-arrow-up w-4 h-4"></div>
                            </template>
                          </a-button>
                        </a-tooltip>
                      </div>
                    </div>
                  </template>

                  <div class="space-y-3">
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-600 dark:text-gray-400">
                        共 {{ image.urls?.length || 0 }} 个链接
                      </span>
                      <a-tag :color="getStatusClasses(image).includes('green') ? 'success' : 'error'" size="small">
                        {{ getStatusText(image) }}
                      </a-tag>
                    </div>

                    <!-- URL列表 -->
                    <div v-if="image.urls?.length" class="space-y-2">
                      <div v-for="(urlRecord, index) in image.urls" :key="index"
                        class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <!-- 图床信息头部 -->
                        <div class="flex items-center justify-between mb-2">
                          <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {{ urlRecord.hostName }}
                            </span>
                            <a-tag
                              :color="urlRecord.status === 'active' ? 'success' : urlRecord.status === 'failed' ? 'error' : 'default'"
                              size="small">
                              {{ urlRecord.status === 'active' ? '正常' : urlRecord.status === 'failed' ? '失效' : '未知' }}
                            </a-tag>
                          </div>
                          <div class="flex items-center space-x-1">
                            <a-tooltip title="复制链接">
                              <a-button @click="copyUrl(urlRecord.url)" type="text" size="small">
                                <template #icon>
                                  <div class="i-heroicons-clipboard w-4 h-4"></div>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip title="删除链接">
                              <a-button @click="deleteUrl(urlRecord, index)" type="text" size="small" danger>
                                <template #icon>
                                  <div class="i-heroicons-trash w-4 h-4"></div>
                                </template>
                              </a-button>
                            </a-tooltip>
                          </div>
                        </div>
                        <!-- URL显示 -->
                        <div class="text-xs text-gray-500 dark:text-gray-400 break-all mb-1">
                          {{ urlRecord.url }}
                        </div>
                        <!-- 上传时间 -->
                        <div class="text-xs text-gray-400 dark:text-gray-500">
                          上传时间: {{ formatDate(urlRecord.uploadTime) }}
                        </div>
                      </div>
                    </div>

                    <!-- 空状态 -->
                    <div v-else class="text-center py-4 text-gray-500 dark:text-gray-400">
                      <div class="i-heroicons-photo w-8 h-8 mx-auto mb-2 opacity-50"></div>
                      <p class="text-sm">暂无备份链接</p>
                      <p class="text-xs mt-1">点击上方按钮添加或上传</p>
                    </div>
                  </div>
                </a-card>

                <!-- 添加URL模态框 -->
                <div v-if="showAddUrlModal"
                  class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
                  @click="handleAddUrlBackdropClick">
                  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4" @click.stop>
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">添加备份URL</h4>
                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">URL地址</label>
                        <input v-model="newUrlInput" type="url"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          placeholder="请输入图片URL">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">图床名称</label>
                        <input v-model="newUrlHostName" type="text"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          placeholder="请输入图床名称">
                      </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                      <button @click="showAddUrlModal = false"
                        class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors">
                        取消
                      </button>
                      <button @click="addNewUrl"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        :disabled="!newUrlInput || !newUrlHostName">
                        添加
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 标签管理卡片 -->
                <a-card size="small" class="tags-card">
                  <template #title>
                    <div class="flex items-center justify-between">
                      <span>标签管理</span>
                      <a-tooltip :title="isEditingTags ? '完成编辑' : '编辑标签'">
                        <a-button @click="toggleTagEditing" type="text" size="small">
                          <template #icon>
                            <div v-if="isEditingTags" class="i-heroicons-check w-4 h-4 text-green-600"></div>
                            <div v-else class="i-heroicons-pencil w-4 h-4"></div>
                          </template>
                        </a-button>
                      </a-tooltip>
                    </div>
                  </template>

                  <!-- 标签显示模式 -->
                  <div v-if="!isEditingTags" class="space-y-3">
                    <div v-if="image.tags?.length" class="flex flex-wrap gap-2">
                      <a-tag v-for="tag in image.tags" :key="tag" color="blue" class="tag-item">
                        {{ tag }}
                      </a-tag>
                    </div>
                    <div v-else class="text-center py-4 text-gray-500 dark:text-gray-400">
                      <div class="i-heroicons-tag w-8 h-8 mx-auto mb-2 opacity-50"></div>
                      <p class="text-sm">暂无标签</p>
                      <a-button @click="toggleTagEditing" type="link" size="small" class="mt-2">
                        添加标签
                      </a-button>
                    </div>
                  </div>

                  <!-- 标签编辑模式 -->
                  <div v-else class="space-y-3">
                    <!-- 当前标签 -->
                    <div v-if="editingTags.length > 0" class="flex flex-wrap gap-2">
                      <a-tag v-for="(tag, index) in editingTags" :key="index" color="blue" closable
                        @close="removeTag(index)" class="tag-item">
                        {{ tag }}
                      </a-tag>
                    </div>

                    <!-- 添加新标签 -->
                    <div class="flex items-center space-x-2">
                      <a-input ref="tagInput" v-model:value="newTagInput" placeholder="输入标签名称..." size="small"
                        @keyup.enter="addTag" @keyup.esc="newTagInput = ''" />
                      <a-button @click="addTag" :disabled="!newTagInput.trim()" type="primary" size="small">
                        添加
                      </a-button>
                    </div>

                    <!-- 可用标签建议 -->
                    <div v-if="availableTags.length > 0" class="space-y-2">
                      <label class="text-xs font-medium text-gray-500 dark:text-gray-400">常用标签</label>
                      <div class="flex flex-wrap gap-1">
                        <a-tag v-for="tag in availableTags" :key="tag" @click="addExistingTag(tag)"
                          class="cursor-pointer hover:opacity-80 transition-opacity">
                          {{ tag }}
                        </a-tag>
                      </div>
                    </div>
                  </div>
                </a-card>
              </div>
            </div>

            <!-- 底部操作栏 - 紧凑型设计 -->
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-3 bg-gray-50 dark:bg-gray-900">
              <div class="grid grid-cols-2 gap-2">
                <a-button @click="copyImageUrl" size="small">
                  <template #icon>
                    <div class="i-heroicons-clipboard w-4 h-4"></div>
                  </template>
                  复制链接
                </a-button>
                <a-button @click="downloadImage" size="small">
                  <template #icon>
                    <div class="i-heroicons-arrow-down-tray w-4 h-4"></div>
                  </template>
                  下载
                </a-button>
                <a-button @click="editImage" size="small">
                  <template #icon>
                    <div class="i-heroicons-pencil w-4 h-4"></div>
                  </template>
                  编辑
                </a-button>
                <a-button @click="deleteImage" danger size="small">
                  <template #icon>
                    <div class="i-heroicons-trash w-4 h-4"></div>
                  </template>
                  删除
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import VideoJsPlayer from '@/components/video/VideoJsPlayer.vue'

// 类型定义
interface ImageRecord {
  id: string
  originalName: string
  size: number
  width?: number
  height?: number
  type: string
  uploadTime: Date
  tags: string[]
  urls: ImageUrlRecord[]
}

interface ImageUrlRecord {
  id?: string
  hostId: string
  hostName: string
  url: string
  deleteUrl?: string
  uploadTime: Date
  status: 'active' | 'failed' | 'unknown'
}

interface Props {
  image: ImageRecord
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  edit: [image: ImageRecord]
  delete: [imageId: string]
}>()

// 消息提示函数
const showSuccess = (msg: string) => {
  message.success(msg)
}

const showError = (msg: string) => {
  message.error(msg)
}

const visible = ref(false)
const scale = ref(1)
const translateX = ref(0)
const translateY = ref(0)
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const imageContainer = ref<HTMLElement>()
const imageElement = ref<HTMLImageElement>()
const videoElement = ref<HTMLVideoElement>()
const actualImageSize = ref({ width: 0, height: 0 })

// 检测是否为视频
const isVideo = computed(() => {
  if (!props.image?.type) return false
  return props.image.type.startsWith('video/')
})

// 备份管理相关状态
const showAddUrlModal = ref(false)
const newUrlInput = ref('')
const newUrlHostName = ref('')
const isUploading = ref(false)

// 编辑相关状态
const isEditingName = ref(false)
const editingName = ref('')
const nameInput = ref<HTMLInputElement>()

const isEditingTags = ref(false)
const editingTags = ref<string[]>([])
const newTagInput = ref('')
const tagInput = ref<HTMLInputElement>()
const availableTags = ref<string[]>([])

// Instagram风格的固定尺寸
const INSTAGRAM_PANEL_WIDTH = 400 // 右侧信息面板宽度

// Instagram风格模态窗口样式计算 - 使用全屏高度
const instagramModalStyle = computed(() => {
  // 获取媒体尺寸（图片或视频）
  const mediaWidth = actualImageSize.value.width || props.image.width || 800
  const mediaHeight = actualImageSize.value.height || props.image.height || 600

  // 计算媒体宽高比
  const mediaAspectRatio = mediaWidth / mediaHeight

  // 使用视窗尺寸，但留出导航栏和适当边距
  const topMargin = 80 // 为导航栏留出空间
  const bottomMargin = 40 // 底部边距
  const sideMargin = 20 // 左右边距

  const availableWidth = window.innerWidth - (sideMargin * 2)
  const availableHeight = window.innerHeight - topMargin - bottomMargin

  // 计算图片容器的理想尺寸
  let imageContainerWidth = availableWidth - INSTAGRAM_PANEL_WIDTH
  let imageContainerHeight = availableHeight

  // 根据媒体宽高比调整容器尺寸，让媒体能够完美填充
  const containerAspectRatio = imageContainerWidth / imageContainerHeight

  if (mediaAspectRatio > containerAspectRatio) {
    // 媒体更宽，以宽度为准，高度自适应
    imageContainerHeight = imageContainerWidth / mediaAspectRatio
  } else {
    // 媒体更高，以高度为准，宽度自适应
    imageContainerWidth = imageContainerHeight * mediaAspectRatio
  }

  // 确保不超过屏幕尺寸
  imageContainerWidth = Math.min(imageContainerWidth, availableWidth - INSTAGRAM_PANEL_WIDTH)
  imageContainerHeight = Math.min(imageContainerHeight, availableHeight)

  // 确保最小尺寸
  imageContainerWidth = Math.max(400, imageContainerWidth)
  imageContainerHeight = Math.max(300, imageContainerHeight)

  // 计算总宽度和高度
  const totalWidth = imageContainerWidth + INSTAGRAM_PANEL_WIDTH
  const totalHeight = imageContainerHeight

  console.log('Instagram全屏模态窗口计算:', {
    mediaWidth,
    mediaHeight,
    mediaAspectRatio,
    availableWidth,
    availableHeight,
    imageContainerWidth,
    imageContainerHeight,
    totalWidth,
    totalHeight,
    windowSize: `${window.innerWidth}x${window.innerHeight}`
  })

  return {
    width: `${totalWidth}px`,
    height: `${totalHeight}px`,
    maxWidth: '100vw',
    maxHeight: '100vh'
  }
})

// 媒体容器样式计算（支持图片和视频）
const imageContainerStyle = computed(() => {
  // 从模态窗口样式中提取总宽度
  const totalWidth = parseInt(instagramModalStyle.value.width)
  const imageContainerWidth = totalWidth - INSTAGRAM_PANEL_WIDTH
  const imageContainerHeight = parseInt(instagramModalStyle.value.height)

  return {
    width: `${imageContainerWidth}px`,
    height: `${imageContainerHeight}px`,
    flexShrink: '0'
  }
})

// 获取主要图片URL
const getPrimaryImageUrl = (image: ImageRecord): string => {
  const activeUrl = image.urls.find(url => url.status === 'active')
  return activeUrl?.url || image.urls[0]?.url || ''
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// 获取状态样式
const getStatusClasses = (image: ImageRecord): string => {
  const hasActiveUrls = image.urls.some(url => url.status === 'active')
  if (!hasActiveUrls) return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
  return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
}

// 获取状态文本
const getStatusText = (image: ImageRecord): string => {
  const hasActiveUrls = image.urls.some(url => url.status === 'active')
  if (!hasActiveUrls) return '失效'
  return '正常'
}

// 缩放控制
const zoomIn = () => {
  if (scale.value < 5) {
    scale.value = Math.min(5, scale.value * 1.2)
  }
}

const zoomOut = () => {
  if (scale.value > 0.1) {
    scale.value = Math.max(0.1, scale.value / 1.2)
  }
}

const resetZoom = () => {
  scale.value = 1
  translateX.value = 0
  translateY.value = 0
}

// 鼠标滚轮缩放
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  if (event.deltaY < 0) {
    zoomIn()
  } else {
    zoomOut()
  }
}

// 拖拽功能
const startDrag = (event: MouseEvent) => {
  if (scale.value <= 1) return

  isDragging.value = true
  dragStart.value = {
    x: event.clientX - translateX.value,
    y: event.clientY - translateY.value
  }

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value) return

  translateX.value = event.clientX - dragStart.value.x
  translateY.value = event.clientY - dragStart.value.y
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 图片加载完成
const handleImageLoad = () => {
  resetZoom()

  // 获取实际图片尺寸
  if (imageElement.value) {
    actualImageSize.value = {
      width: imageElement.value.naturalWidth,
      height: imageElement.value.naturalHeight
    }

    // 强制重新计算模态窗口宽度
    nextTick(() => {
      // 触发响应式更新
      console.log('图片加载完成，尺寸:', actualImageSize.value)
    })
  }
}

// 视频加载完成
const handleVideoLoad = (dimensions?: { width: number; height: number; duration?: number }) => {
  // 如果从ModernVideoPlayer组件传来尺寸数据，直接使用
  if (dimensions && dimensions.width && dimensions.height) {
    actualImageSize.value = {
      width: dimensions.width,
      height: dimensions.height
    }
    console.log('从ModernVideoPlayer获取视频尺寸:', actualImageSize.value)
  }
  // 否则尝试从videoElement获取（兼容性处理）
  else if (videoElement.value) {
    actualImageSize.value = {
      width: videoElement.value.videoWidth,
      height: videoElement.value.videoHeight
    }
    console.log('从videoElement获取视频尺寸:', actualImageSize.value)
  }

  // 强制重新计算模态窗口宽度
  nextTick(() => {
    // 触发响应式更新
    console.log('视频加载完成，最终尺寸:', actualImageSize.value)
  })
}

// Plyr播放器就绪
const handlePlayerReady = (player: any) => {
  console.log('Plyr播放器就绪:', player)
}

// 视频播放错误
const handleVideoError = (error: any) => {
  console.error('视频播放错误:', error)
  showError('视频播放失败')
}

// 复制图片链接
const copyImageUrl = async () => {
  const url = getPrimaryImageUrl(props.image)
  try {
    await navigator.clipboard.writeText(url)
    showSuccess('链接复制成功')
  } catch (err) {
    showError('复制失败')
  }
}

// 复制指定URL
const copyUrl = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    showSuccess('链接复制成功')
  } catch (err) {
    showError('复制失败')
  }
}

// 删除指定URL
const deleteUrl = async (urlRecord: any, index: number) => {
  if (!confirm(`确定要删除来自 ${urlRecord.hostName} 的链接吗？`)) {
    return
  }

  try {
    // 模拟删除操作
    props.image.urls.splice(index, 1)
    showSuccess('链接删除成功')

    // 如果没有URL了，发出删除事件
    if (props.image.urls.length === 0) {
      emit('delete', props.image.id.toString())
    }
  } catch (err) {
    showError('删除失败')
  }
}

// 添加新URL
const addNewUrl = async () => {
  if (!newUrlInput.value || !newUrlHostName.value) {
    showError('请填写完整的URL和图床名称')
    return
  }

  try {
    const newUrlRecord: ImageUrlRecord = {
      hostId: 'manual',
      hostName: newUrlHostName.value,
      url: newUrlInput.value,
      deleteUrl: undefined,
      uploadTime: new Date(),
      status: 'active' as const
    }

    // 添加到本地数组
    props.image.urls.push(newUrlRecord)

    // 重置表单
    newUrlInput.value = ''
    newUrlHostName.value = ''
    showAddUrlModal.value = false

    showSuccess('URL添加成功')
  } catch (err) {
    showError('添加失败')
  }
}

// 文件名编辑相关方法
const startEditingName = () => {
  isEditingName.value = true
  editingName.value = props.image.originalName
  nextTick(() => {
    nameInput.value?.focus()
    nameInput.value?.select()
  })
}

const saveNameEdit = async () => {
  if (!editingName.value.trim()) {
    cancelNameEdit()
    return
  }

  if (editingName.value === props.image.originalName) {
    cancelNameEdit()
    return
  }

  try {
    // 更新本地数据
    props.image.originalName = editingName.value.trim()
    isEditingName.value = false
    showSuccess('文件名更新成功')
  } catch (err) {
    showError('更新失败')
    cancelNameEdit()
  }
}

const cancelNameEdit = () => {
  isEditingName.value = false
  editingName.value = ''
}

// 标签编辑相关方法
const toggleTagEditing = async () => {
  if (isEditingTags.value) {
    // 保存标签更改
    await saveTagChanges()
  } else {
    // 开始编辑
    isEditingTags.value = true
    editingTags.value = [...(props.image.tags || [])]
    newTagInput.value = ''

    // 加载可用标签
    await loadAvailableTags()
  }
}

const addTag = () => {
  const tagName = newTagInput.value.trim()
  if (!tagName) return

  if (editingTags.value.includes(tagName)) {
    showError('标签已存在')
    return
  }

  editingTags.value.push(tagName)
  newTagInput.value = ''

  // 从可用标签中移除
  const index = availableTags.value.indexOf(tagName)
  if (index > -1) {
    availableTags.value.splice(index, 1)
  }
}

const addExistingTag = (tagName: string) => {
  if (editingTags.value.includes(tagName)) return

  editingTags.value.push(tagName)

  // 从可用标签中移除
  const index = availableTags.value.indexOf(tagName)
  if (index > -1) {
    availableTags.value.splice(index, 1)
  }
}

const removeTag = (index: number) => {
  const removedTag = editingTags.value.splice(index, 1)[0]

  // 添加回可用标签（如果不在列表中）
  if (!availableTags.value.includes(removedTag)) {
    availableTags.value.push(removedTag)
    availableTags.value.sort()
  }
}

const saveTagChanges = async () => {
  try {
    // 更新本地数据
    props.image.tags = [...editingTags.value]
    isEditingTags.value = false
    showSuccess('标签更新成功')
  } catch (err) {
    showError('更新失败')
  }
}

const loadAvailableTags = async () => {
  try {
    // 模拟一些常用标签
    const mockTags = ['风景', '人物', '动物', '建筑', '美食', '旅行', '生活', '工作']
    availableTags.value = mockTags
      .filter(tagName => !editingTags.value.includes(tagName))
      .sort()
  } catch (err) {
    console.error('加载可用标签失败:', err)
    availableTags.value = []
  }
}



// 上传到随机图床
const uploadToRandomHost = async () => {
  if (isUploading.value) return

  try {
    isUploading.value = true

    // 模拟上传过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟成功上传
    const newUrlRecord: ImageUrlRecord = {
      hostId: 'demo',
      hostName: '演示图床',
      url: getPrimaryImageUrl(props.image),
      deleteUrl: undefined,
      uploadTime: new Date(),
      status: 'active' as const
    }

    // 添加到本地数组
    props.image.urls.push(newUrlRecord)
    showSuccess('上传成功')
  } catch (err) {
    console.error('上传到图床失败:', err)
    showError('上传失败')
  } finally {
    isUploading.value = false
  }
}

// 处理添加URL模态框背景点击
const handleAddUrlBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    showAddUrlModal.value = false
    newUrlInput.value = ''
    newUrlHostName.value = ''
  }
}

// 下载图片
const downloadImage = () => {
  const url = getPrimaryImageUrl(props.image)
  const link = document.createElement('a')
  link.href = url
  link.download = props.image.originalName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 编辑图片
const editImage = () => {
  emit('edit', props.image)
}

// 删除图片
const deleteImage = () => {
  if (confirm('确定要删除这张图片吗？此操作不可撤销。')) {
    emit('delete', props.image.id)
  }
}

// 关闭模态框
const close = () => {
  visible.value = false
  setTimeout(() => {
    emit('close')
  }, 300)
}

// 点击背景关闭
const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    close()
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    close()
  }
}

// 窗口尺寸变化处理
const handleResize = () => {
  // 触发响应式更新，重新计算模态窗口尺寸
  console.log('窗口尺寸变化，重新计算模态窗口尺寸')
}

onMounted(() => {
  visible.value = true
  document.addEventListener('keydown', handleKeydown)
  window.addEventListener('resize', handleResize)

  // 立即设置媒体尺寸（如果 props 中有的话）
  if (props.image.width && props.image.height) {
    actualImageSize.value = {
      width: props.image.width,
      height: props.image.height
    }
    console.log('从 props 设置媒体尺寸:', actualImageSize.value, isVideo.value ? '(视频)' : '(图片)')
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* Instagram风格模态窗口 - 响应式优化 */
.instagram-modal {
  max-width: calc(100vw - 40px);
  /* 左右各留20px边距 */
  max-height: calc(100vh - 120px);
  /* 上边距80px + 下边距40px */
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Instagram风格图片容器 */
.instagram-image-container {
  position: relative;
  overflow: hidden;
}

/* Instagram风格图片 */
.instagram-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  user-select: none;
  -webkit-user-drag: none;
}

/* Instagram风格媒体（视频） */
.instagram-media {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  user-select: none;
}

/* 视频播放器样式 */
.instagram-media::-webkit-media-controls-panel {
  background-color: rgba(0, 0, 0, 0.8);
}

.instagram-media::-webkit-media-controls-play-button,
.instagram-media::-webkit-media-controls-volume-slider,
.instagram-media::-webkit-media-controls-timeline,
.instagram-media::-webkit-media-controls-current-time-display,
.instagram-media::-webkit-media-controls-time-remaining-display {
  color: white;
}

/* 信息面板样式 - 固定宽度 */
.info-panel {
  width: 400px;
  flex-shrink: 0;
  border-left: 1px solid #e5e7eb;
}

.dark .info-panel {
  border-left-color: #374151;
}

/* 关闭按钮样式优化 */
.fixed.top-24.right-4 {
  transition: all 0.2s ease;
}

.fixed.top-24.right-4:hover {
  transform: scale(1.1);
}

/* 配置面板头部样式 */
.config-header {
  background: linear-gradient(135deg, var(--ant-color-bg-container) 0%, var(--ant-color-bg-layout) 100%);
}

.card-title-wrapper {
  display: flex;
  align-items: center;
}

.card-title {
  background: linear-gradient(135deg, var(--ant-color-primary) 0%, var(--ant-color-primary-hover) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.help-icon-btn {
  color: var(--ant-color-text-tertiary);
  transition: all 0.2s ease;
}

.help-icon-btn:hover {
  color: var(--ant-color-primary);
  transform: scale(1.1);
}

/* 配置内容样式 */
.config-content {
  background: var(--ant-color-bg-container);
}

/* 卡片样式 */
.info-card,
.backup-card,
.tags-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.info-card:hover,
.backup-card:hover,
.tags-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 标签样式 */
.tag-item {
  transition: all 0.2s ease;
}

.tag-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}



/* 响应式设计 - 移动端全屏优化 */
@media (max-width: 768px) {

  /* 移动端容器样式重置 */
  .fixed.inset-0.z-50.flex.items-center.justify-center.pt-20.pb-10.px-5 {
    padding: 0 !important;
  }

  .instagram-modal {
    flex-direction: column;
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
    box-shadow: none !important;
  }

  .instagram-image-container {
    width: 100vw !important;
    height: 60vh !important;
  }

  .info-panel {
    width: 100vw !important;
    height: 40vh !important;
    border-left: none;
    border-top: 1px solid #e5e7eb;
    overflow-y: auto;
  }

  .dark .info-panel {
    border-top-color: #374151;
  }

  /* 移动设备上调整关闭按钮位置 */
  .fixed.top-24.right-4 {
    top: 1rem;
    right: 1rem;
    z-index: 70;
  }
}

/* 确保全屏时的完美适配 */
@media (min-width: 769px) {
  .instagram-modal {
    border-radius: 8px;
    /* 桌面端保留圆角 */
  }
}
</style>

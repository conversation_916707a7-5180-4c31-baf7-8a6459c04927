<template>
  <div ref="playerContainer" class="simple-video-container">
    <video
      ref="videoElement"
      :src="src"
      :poster="poster"
      preload="metadata"
      crossorigin="anonymous"
      playsinline
      controls
      @loadedmetadata="handleLoadedMetadata"
      @error="handleError"
      @play="$emit('play')"
      @pause="$emit('pause')"
      @ended="$emit('ended')"
      @timeupdate="handleTimeUpdate"
      @volumechange="handleVolumeChange"
      class="video-player"
    >
      <source :src="src" :type="videoType" />
      您的浏览器不支持视频播放
    </video>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'

interface Props {
  src: string
  poster?: string
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
  controls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: false,
  muted: false,
  loop: false,
  controls: true,
})

interface Emits {
  ready: [element: HTMLVideoElement]
  play: []
  pause: []
  ended: []
  timeupdate: [currentTime: number]
  volumechange: [volume: number]
  error: [error: any]
  loadedmetadata: [dimensions: { width: number; height: number }]
}

const emit = defineEmits<Emits>()

const playerContainer = ref<HTMLElement>()
const videoElement = ref<HTMLVideoElement>()

// 获取视频MIME类型
const videoType = computed(() => {
  const url = props.src.toLowerCase()
  if (url.includes('.mp4')) return 'video/mp4'
  if (url.includes('.webm')) return 'video/webm'
  if (url.includes('.ogg') || url.includes('.ogv')) return 'video/ogg'
  if (url.includes('.avi')) return 'video/x-msvideo'
  if (url.includes('.mov')) return 'video/quicktime'
  return 'video/mp4' // 默认
})

// 处理视频元数据加载
const handleLoadedMetadata = () => {
  if (videoElement.value) {
    emit('loadedmetadata', {
      width: videoElement.value.videoWidth,
      height: videoElement.value.videoHeight
    })
    emit('ready', videoElement.value)
  }
}

// 处理播放错误
const handleError = (error: any) => {
  console.error('视频加载错误:', error)
  emit('error', error)
}

// 处理时间更新
const handleTimeUpdate = () => {
  if (videoElement.value) {
    emit('timeupdate', videoElement.value.currentTime)
  }
}

// 处理音量变化
const handleVolumeChange = () => {
  if (videoElement.value) {
    emit('volumechange', videoElement.value.volume)
  }
}

// 暴露播放器方法
defineExpose({
  videoElement: videoElement,
  play: () => videoElement.value?.play(),
  pause: () => videoElement.value?.pause(),
  seek: (time: number) => {
    if (videoElement.value) {
      videoElement.value.currentTime = time
    }
  },
  setVolume: (volume: number) => {
    if (videoElement.value) {
      videoElement.value.volume = volume
    }
  },
  mute: () => {
    if (videoElement.value) {
      videoElement.value.muted = true
    }
  },
  unmute: () => {
    if (videoElement.value) {
      videoElement.value.muted = false
    }
  }
})

onMounted(async () => {
  await nextTick()
  if (videoElement.value) {
    // 设置初始属性
    videoElement.value.autoplay = props.autoplay
    videoElement.value.muted = props.muted
    videoElement.value.loop = props.loop
    videoElement.value.controls = props.controls
  }
})
</script>

<style scoped>
.simple-video-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

/* 自定义视频控制条样式 */
.video-player::-webkit-media-controls-panel {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
}

.video-player::-webkit-media-controls-play-button,
.video-player::-webkit-media-controls-volume-slider,
.video-player::-webkit-media-controls-timeline,
.video-player::-webkit-media-controls-current-time-display,
.video-player::-webkit-media-controls-time-remaining-display,
.video-player::-webkit-media-controls-fullscreen-button {
  color: white;
  filter: brightness(1.2);
}

.video-player::-webkit-media-controls-timeline {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.video-player::-webkit-media-controls-current-time-display,
.video-player::-webkit-media-controls-time-remaining-display {
  font-size: 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Firefox样式 */
.video-player::-moz-media-controls {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
}

/* 悬停效果 */
.simple-video-container:hover .video-player::-webkit-media-controls-panel {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-player::-webkit-media-controls-panel {
    padding: 8px;
  }
  
  .video-player::-webkit-media-controls-play-button {
    width: 40px;
    height: 40px;
  }
}

/* 暗色主题适配 */
.dark .simple-video-container {
  background: #1a1a1a;
}

.dark .video-player {
  background: #1a1a1a;
}
</style>

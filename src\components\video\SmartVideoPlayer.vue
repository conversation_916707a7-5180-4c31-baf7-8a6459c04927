<template>
  <div ref="playerContainer" class="smart-video-player">
    <!-- 受保护视频的iframe播放器 -->
    <iframe
      v-if="playbackMode === 'iframe'"
      ref="iframeElement"
      :src="iframeSrc"
      class="video-iframe"
      frameborder="0"
      allowfullscreen
      allow="autoplay; encrypted-media; picture-in-picture"
      @load="handleIframeLoad"
    ></iframe>

    <!-- 代理视频播放器 -->
    <video
      v-else-if="playbackMode === 'proxy'"
      ref="videoElement"
      :src="proxySrc"
      :poster="poster"
      preload="metadata"
      playsinline
      controls
      @loadedmetadata="handleLoadedMetadata"
      @error="handleProxyError"
      class="video-element"
    >
      您的浏览器不支持视频播放
    </video>

    <!-- 普通视频播放器 -->
    <ModernVideoPlayer
      v-else
      :src="src"
      :poster="poster"
      :autoplay="autoplay"
      :muted="muted"
      :loop="loop"
      @ready="$emit('ready', $event)"
      @loadedmetadata="$emit('loadedmetadata', $event)"
      @error="handleDirectError"
      @play="$emit('play')"
      @pause="$emit('pause')"
      @ended="$emit('ended')"
      @timeupdate="$emit('timeupdate', $event)"
      @volumechange="$emit('volumechange', $event)"
    />

    <!-- 播放模式切换提示 -->
    <div v-if="showModeSwitch" class="mode-switch-overlay">
      <div class="mode-switch-content">
        <div class="i-heroicons-exclamation-triangle w-8 h-8 text-yellow-500 mb-2"></div>
        <p class="text-sm text-gray-700 mb-3">视频无法直接播放，正在尝试其他播放方式...</p>
        <div class="flex space-x-2">
          <button @click="tryIframeMode" class="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600">
            网页播放
          </button>
          <button @click="tryProxyMode" class="px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600">
            代理播放
          </button>
          <button @click="openInNewTab" class="px-3 py-1 bg-purple-500 text-white rounded text-xs hover:bg-purple-600">
            新窗口打开
          </button>
        </div>
      </div>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p class="loading-text">{{ loadingText }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import ModernVideoPlayer from './ModernVideoPlayer.vue'

interface Props {
  src: string
  poster?: string
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: false,
  muted: false,
  loop: false,
})

interface Emits {
  ready: [element: HTMLVideoElement | HTMLIFrameElement]
  play: []
  pause: []
  ended: []
  timeupdate: [currentTime: number]
  volumechange: [volume: number]
  error: [error: any]
  loadedmetadata: [dimensions: { width: number; height: number; duration?: number }]
}

const emit = defineEmits<Emits>()

const playerContainer = ref<HTMLElement>()
const videoElement = ref<HTMLVideoElement>()
const iframeElement = ref<HTMLIFrameElement>()

// 播放模式：direct(直接), iframe(网页), proxy(代理)
const playbackMode = ref<'direct' | 'iframe' | 'proxy'>('direct')
const showModeSwitch = ref(false)
const isLoading = ref(false)
const loadingText = ref('正在加载视频...')

// 处理后的视频源
const iframeSrc = ref('')
const proxySrc = ref('')

// 检测是否为受保护的视频平台
const isProtectedPlatform = (url: string): boolean => {
  const protectedPatterns = [
    /douyin\.com/i,
    /tiktok\.com/i,
    /kuaishou\.com/i,
    /bilibili\.com/i,
    /iqiyi\.com/i,
    /youku\.com/i,
    /v\.qq\.com/i,
    /aweme\/v\d+\/play/i, // 抖音API
    /short-video\/\d+/i,  // 快手API
  ]
  
  return protectedPatterns.some(pattern => pattern.test(url))
}

// 生成代理URL（这里可以配置你的代理服务器）
const generateProxyUrl = (originalUrl: string): string => {
  // 方案1: 使用公共代理服务（注意：生产环境需要自己的代理服务器）
  // return `https://cors-anywhere.herokuapp.com/${originalUrl}`
  
  // 方案2: 使用你自己的代理服务器
  // return `https://your-proxy-server.com/proxy?url=${encodeURIComponent(originalUrl)}`
  
  // 方案3: 添加特殊headers尝试绕过限制
  return originalUrl + (originalUrl.includes('?') ? '&' : '?') + 'referrer=direct'
}

// 智能选择播放模式
const selectPlaybackMode = () => {
  const url = props.src
  
  if (isProtectedPlatform(url)) {
    console.log('检测到受保护平台，使用iframe模式:', url)
    playbackMode.value = 'iframe'
    iframeSrc.value = url
  } else {
    console.log('使用直接播放模式:', url)
    playbackMode.value = 'direct'
  }
}

// 尝试iframe模式
const tryIframeMode = () => {
  console.log('切换到iframe播放模式')
  playbackMode.value = 'iframe'
  iframeSrc.value = props.src
  showModeSwitch.value = false
  isLoading.value = true
  loadingText.value = '正在加载网页播放器...'
}

// 尝试代理模式
const tryProxyMode = () => {
  console.log('切换到代理播放模式')
  playbackMode.value = 'proxy'
  proxySrc.value = generateProxyUrl(props.src)
  showModeSwitch.value = false
  isLoading.value = true
  loadingText.value = '正在通过代理加载视频...'
}

// 在新窗口打开
const openInNewTab = () => {
  window.open(props.src, '_blank')
  showModeSwitch.value = false
}

// 直接播放失败处理
const handleDirectError = (error: any) => {
  console.error('直接播放失败:', error)
  
  if (isProtectedPlatform(props.src)) {
    console.log('尝试其他播放方式')
    showModeSwitch.value = true
    // 自动尝试iframe模式
    setTimeout(() => {
      if (showModeSwitch.value) {
        tryIframeMode()
      }
    }, 2000)
  } else {
    emit('error', error)
  }
}

// 代理播放失败处理
const handleProxyError = (error: any) => {
  console.error('代理播放失败:', error)
  showModeSwitch.value = true
  isLoading.value = false
}

// iframe加载完成
const handleIframeLoad = () => {
  console.log('iframe播放器加载完成')
  isLoading.value = false
  
  emit('loadedmetadata', {
    width: 1920,
    height: 1080,
    duration: 0
  })
  
  if (iframeElement.value) {
    emit('ready', iframeElement.value)
  }
}

// 视频元数据加载完成
const handleLoadedMetadata = () => {
  isLoading.value = false
  
  if (videoElement.value) {
    emit('loadedmetadata', {
      width: videoElement.value.videoWidth,
      height: videoElement.value.videoHeight,
      duration: videoElement.value.duration
    })
    emit('ready', videoElement.value)
  }
}

onMounted(() => {
  selectPlaybackMode()
})
</script>

<style scoped>
.smart-video-player {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
}

.video-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #000;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

.mode-switch-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.mode-switch-content {
  background: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  max-width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.loading-text {
  color: white;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 暗色主题适配 */
.dark .mode-switch-content {
  background: #1a1a1a;
  color: white;
}
</style>

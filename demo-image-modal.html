<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片详情模态窗口演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .demo-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .demo-description {
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }
        
        .demo-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
        }
        
        .features-list {
            margin-top: 2rem;
            padding: 0;
            list-style: none;
        }
        
        .features-list li {
            padding: 8px 0;
            color: #374151;
            position: relative;
            padding-left: 24px;
        }
        
        .features-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }
        
        .dark-mode-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #374151;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .dark-mode-toggle:hover {
            background: #4b5563;
        }
        
        /* 暗黑模式样式 */
        body.dark {
            background: #111827;
            color: #f9fafb;
        }
        
        body.dark .demo-container {
            background: #1f2937;
            color: #f9fafb;
        }
        
        body.dark .demo-title {
            color: #f9fafb;
        }
        
        body.dark .demo-description {
            color: #d1d5db;
        }
        
        body.dark .features-list li {
            color: #e5e7eb;
        }
    </style>
</head>
<body>
    <button class="dark-mode-toggle" onclick="toggleDarkMode()">🌙 切换暗黑模式</button>
    
    <div class="demo-container">
        <h1 class="demo-title">图片详情模态窗口演示</h1>
        <p class="demo-description">
            这是一个经过优化的图片详情模态窗口，具有以下特性：
        </p>
        
        <ul class="features-list">
            <li>移除了顶部标题栏，提供更沉浸的体验</li>
            <li>关闭按钮移至页面右上角，浮动显示</li>
            <li>添加了浅灰色半透明背景蒙版</li>
            <li>优化了图片显示逻辑，智能填充可用空间</li>
            <li>保持图片原始宽高比，不会变形</li>
            <li>完整支持暗黑模式切换</li>
            <li>响应式设计，适配移动设备</li>
        </ul>
        
        <button class="demo-button" onclick="openImageModal()">
            🖼️ 打开图片详情
        </button>
    </div>
    
    <script>
        function toggleDarkMode() {
            document.body.classList.toggle('dark');
            const isDark = document.body.classList.contains('dark');
            localStorage.setItem('darkMode', isDark);
        }
        
        function openImageModal() {
            alert('在实际的 Vue 应用中，这里会打开图片详情模态窗口。\n\n请在 Vue 项目中使用 src/test-image-modal.vue 来测试完整功能。');
        }
        
        // 恢复暗黑模式设置
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark');
        }
    </script>
</body>
</html>

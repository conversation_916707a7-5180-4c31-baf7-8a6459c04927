# Vue Easy Lightbox 安装和配置指南

## 1. 安装依赖

```bash
# 使用 npm
npm install vue-easy-lightbox@next

# 使用 yarn
yarn add vue-easy-lightbox@next

# 使用 pnpm
pnpm add vue-easy-lightbox@next
```

## 2. 全局注册（推荐）

在 `src/main.ts` 中注册：

```typescript
import { createApp } from 'vue'
import App from './App.vue'
import VueEasyLightbox from 'vue-easy-lightbox'

const app = createApp(App)

// 注册 vue-easy-lightbox
app.use(VueEasyLightbox)

app.mount('#app')
```

## 3. 局部注册

如果你只想在特定组件中使用：

```vue
<script setup lang="ts">
import VueEasyLightbox from 'vue-easy-lightbox'
</script>

<template>
  <VueEasyLightbox
    :visible="visible"
    :imgs="imgs"
    :index="index"
    @hide="onHide"
  />
</template>
```

## 4. TypeScript 支持

如果使用 TypeScript，可能需要添加类型声明。在 `src/types/vue-easy-lightbox.d.ts` 中：

```typescript
declare module 'vue-easy-lightbox' {
  import { DefineComponent } from 'vue'
  
  interface LightboxImage {
    src: string
    title?: string
    alt?: string
  }
  
  interface LightboxProps {
    visible: boolean
    imgs: string[] | LightboxImage[]
    index: number
    loop?: boolean
    zoomDisabled?: boolean
    moveDisabled?: boolean
    rotateDisabled?: boolean
    maskClosable?: boolean
    scrollDisabled?: boolean
    teleport?: boolean | string
    dblclickDisabled?: boolean
    maxZoom?: number
    minZoom?: number
  }
  
  const VueEasyLightbox: DefineComponent<LightboxProps>
  export default VueEasyLightbox
}
```

## 5. 基本使用示例

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <button @click="showLightbox = true">
      打开图片预览
    </button>
    
    <!-- 图片预览 -->
    <vue-easy-lightbox
      :visible="showLightbox"
      :imgs="images"
      :index="currentIndex"
      @hide="showLightbox = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const showLightbox = ref(false)
const currentIndex = ref(0)

const images = [
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg',
  'https://example.com/image3.jpg'
]
</script>
```

## 6. 高级配置

```vue
<template>
  <vue-easy-lightbox
    :visible="visible"
    :imgs="images"
    :index="index"
    :loop="true"
    :zoom-disabled="false"
    :move-disabled="false"
    :rotate-disabled="true"
    :mask-closable="true"
    :scroll-disabled="true"
    :teleport="true"
    :dblclick-disabled="false"
    :max-zoom="5"
    :min-zoom="0.1"
    @hide="onHide"
    @on-index-change="onIndexChange"
    @on-error="onError"
  >
    <!-- 自定义工具栏 -->
    <template #toolbar="{ toolbarMethods, imgIndex }">
      <div class="custom-toolbar">
        <button @click="toolbarMethods.zoomIn">放大</button>
        <button @click="toolbarMethods.zoomOut">缩小</button>
        <button @click="toolbarMethods.reset">重置</button>
        <span>{{ imgIndex + 1 }} / {{ images.length }}</span>
      </div>
    </template>
    
    <!-- 自定义按钮 -->
    <template #btn-prev="{ prev }">
      <button @click="prev" class="custom-prev-btn">
        上一张
      </button>
    </template>
    
    <template #btn-next="{ next }">
      <button @click="next" class="custom-next-btn">
        下一张
      </button>
    </template>
  </vue-easy-lightbox>
</template>
```

## 7. 事件处理

```typescript
const onHide = () => {
  visible.value = false
}

const onIndexChange = (newIndex: number) => {
  currentIndex.value = newIndex
  console.log('当前图片索引:', newIndex)
}

const onError = (error: any) => {
  console.error('图片加载失败:', error)
  message.error('图片加载失败')
}
```

## 8. 样式自定义

```css
/* 自定义工具栏样式 */
.custom-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.custom-toolbar button {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 0.375rem;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.custom-toolbar button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 自定义导航按钮 */
.custom-prev-btn,
.custom-next-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.custom-prev-btn {
  left: 2rem;
}

.custom-next-btn {
  right: 2rem;
}

.custom-prev-btn:hover,
.custom-next-btn:hover {
  background: rgba(0, 0, 0, 0.8);
}
```

## 9. 完整的 Props 列表

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | boolean | false | 是否显示 |
| imgs | string[] \| object[] | [] | 图片数组 |
| index | number | 0 | 当前图片索引 |
| loop | boolean | false | 是否循环播放 |
| zoomDisabled | boolean | false | 是否禁用缩放 |
| moveDisabled | boolean | false | 是否禁用拖拽 |
| rotateDisabled | boolean | false | 是否禁用旋转 |
| maskClosable | boolean | true | 点击遮罩是否关闭 |
| scrollDisabled | boolean | true | 是否禁用滚动 |
| teleport | boolean \| string | true | 是否传送到 body |
| dblclickDisabled | boolean | false | 是否禁用双击 |
| maxZoom | number | 3 | 最大缩放倍数 |
| minZoom | number | 0.1 | 最小缩放倍数 |

## 10. 事件列表

| 事件 | 参数 | 说明 |
|------|------|------|
| hide | - | 关闭时触发 |
| on-index-change | index: number | 图片切换时触发 |
| on-error | error: any | 图片加载失败时触发 |

## 11. 插槽列表

| 插槽 | 参数 | 说明 |
|------|------|------|
| toolbar | { toolbarMethods, imgIndex } | 自定义工具栏 |
| btn-prev | { prev } | 自定义上一张按钮 |
| btn-next | { next } | 自定义下一张按钮 |
| loading | - | 自定义加载状态 |

这样配置后，你就可以获得一个完美的Instagram风格图片预览体验了！

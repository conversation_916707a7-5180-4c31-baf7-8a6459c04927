<template>
  <div ref="playerContainer" class="modern-video-player" @mousemove="handleMouseMove" @mouseleave="hideControls">
    <!-- 受保护视频的iframe播放器 -->
    <iframe v-if="useIframePlayer" ref="iframeElement" :src="iframeSrc" class="video-iframe" frameborder="0"
      allowfullscreen allow="autoplay; encrypted-media; picture-in-picture" @load="handleIframeLoad"></iframe>

    <!-- 普通视频播放器 -->
    <video v-else ref="videoElement" :src="processedSrc" :poster="poster" preload="metadata" :crossorigin="crossOrigin"
      playsinline @loadedmetadata="handleLoadedMetadata" @error="handleError" @play="handlePlay" @pause="handlePause"
      @ended="handleEnded" @timeupdate="handleTimeUpdate" @volumechange="handleVolumeChange" @click="togglePlay"
      class="video-element">
      <source :src="processedSrc" :type="videoType" />
      您的浏览器不支持视频播放
    </video>

    <!-- 中央播放按钮 -->
    <div v-if="!isPlaying && showCenterPlay" class="center-play-button" @click="play">
      <div class="play-icon">
        <div class="i-heroicons-play-solid w-16 h-16 text-white"></div>
      </div>
    </div>

    <!-- 自定义控制条 -->
    <div v-show="showControls" class="video-controls" :class="{ 'controls-visible': showControls }">
      <!-- 进度条 -->
      <div class="progress-container" @click="handleProgressClick">
        <div class="progress-track">
          <div class="progress-buffer" :style="{ width: `${bufferProgress}%` }"></div>
          <div class="progress-played" :style="{ width: `${playProgress}%` }"></div>
          <div class="progress-thumb" :style="{ left: `${playProgress}%` }"></div>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="controls-row">
        <div class="controls-left">
          <!-- 播放/暂停 -->
          <button @click="togglePlay" class="control-button">
            <div v-if="isPlaying" class="i-heroicons-pause-solid w-5 h-5"></div>
            <div v-else class="i-heroicons-play-solid w-5 h-5"></div>
          </button>

          <!-- 音量控制 -->
          <div class="volume-control">
            <button @click="toggleMute" class="control-button">
              <div v-if="isMuted || volume === 0" class="i-heroicons-speaker-x-mark-solid w-5 h-5"></div>
              <div v-else-if="volume < 0.5" class="i-heroicons-speaker-wave-solid w-5 h-5"></div>
              <div v-else class="i-heroicons-speaker-wave-solid w-5 h-5"></div>
            </button>
            <div class="volume-slider-container">
              <input v-model="volume" type="range" min="0" max="1" step="0.1" class="volume-slider"
                @input="handleVolumeSliderChange" />
            </div>
          </div>

          <!-- 时间显示 -->
          <div class="time-display">
            {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
          </div>
        </div>

        <div class="controls-right">
          <!-- 播放速度 -->
          <div class="speed-control">
            <select v-model="playbackRate" @change="handleSpeedChange" class="speed-select">
              <option value="0.5">0.5x</option>
              <option value="0.75">0.75x</option>
              <option value="1">1x</option>
              <option value="1.25">1.25x</option>
              <option value="1.5">1.5x</option>
              <option value="2">2x</option>
            </select>
          </div>

          <!-- 全屏 -->
          <button @click="toggleFullscreen" class="control-button">
            <div v-if="isFullscreen" class="i-heroicons-arrows-pointing-in-solid w-5 h-5"></div>
            <div v-else class="i-heroicons-arrows-pointing-out-solid w-5 h-5"></div>
          </button>
        </div>
      </div>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-indicator">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

interface Props {
  src: string
  poster?: string
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: false,
  muted: false,
  loop: false,
})

interface Emits {
  ready: [element: HTMLVideoElement]
  play: []
  pause: []
  ended: []
  timeupdate: [currentTime: number]
  volumechange: [volume: number]
  error: [error: any]
  loadedmetadata: [dimensions: { width: number; height: number; duration: number }]
}

const emit = defineEmits<Emits>()

const playerContainer = ref<HTMLElement>()
const videoElement = ref<HTMLVideoElement>()
const iframeElement = ref<HTMLIFrameElement>()

// 播放状态
const isPlaying = ref(false)
const isLoading = ref(false)
const isMuted = ref(props.muted)
const volume = ref(1)
const currentTime = ref(0)
const duration = ref(0)
const playProgress = ref(0)
const bufferProgress = ref(0)
const playbackRate = ref(1)

// UI状态
const showControls = ref(true)
const showCenterPlay = ref(true)
const isFullscreen = ref(false)
let controlsTimeout: ReturnType<typeof setTimeout> | null = null

// 智能播放器状态
const useIframePlayer = ref(false)
const processedSrc = ref('')
const iframeSrc = ref('')
const crossOrigin = ref<string | null>(null)

// 检测是否为API视频链接（需要iframe播放）
const isApiVideoUrl = (url: string): boolean => {
  const apiPatterns = [
    /douyin\.com\/aweme\/v\d+\/play/i,  // 抖音API
    /kuaishou\.com\/short-video/i,      // 快手API
    /bilibili\.com\/video\/av\d+/i,     // B站视频页面
    /bilibili\.com\/video\/BV/i,        // B站BV号
    /v\.qq\.com\/x\/cover/i,            // 腾讯视频
    /iqiyi\.com\/v_/i,                  // 爱奇艺
    /youku\.com\/v_show/i,              // 优酷
  ]

  return apiPatterns.some(pattern => pattern.test(url))
}

// 智能处理视频源
const processVideoSource = () => {
  const url = props.src

  if (isApiVideoUrl(url)) {
    console.log('检测到API视频链接，使用iframe播放:', url)
    useIframePlayer.value = true
    iframeSrc.value = url
    processedSrc.value = ''
    crossOrigin.value = null
  } else {
    console.log('使用普通视频播放器:', url)
    useIframePlayer.value = false
    processedSrc.value = url
    iframeSrc.value = ''
    crossOrigin.value = null
  }
}

// 获取视频MIME类型
const videoType = computed(() => {
  const url = processedSrc.value.toLowerCase()
  if (url.includes('.mp4')) return 'video/mp4'
  if (url.includes('.webm')) return 'video/webm'
  if (url.includes('.ogg') || url.includes('.ogv')) return 'video/ogg'
  return 'video/mp4'
})

// 播放控制
const play = async () => {
  if (videoElement.value) {
    try {
      await videoElement.value.play()
      showCenterPlay.value = false
    } catch (error) {
      console.error('播放失败:', error)
    }
  }
}

const pause = () => {
  if (videoElement.value) {
    videoElement.value.pause()
  }
}

const togglePlay = () => {
  if (isPlaying.value) {
    pause()
  } else {
    play()
  }
}

// 音量控制
const toggleMute = () => {
  if (videoElement.value) {
    videoElement.value.muted = !videoElement.value.muted
    isMuted.value = videoElement.value.muted
  }
}

const handleVolumeSliderChange = () => {
  if (videoElement.value) {
    videoElement.value.volume = volume.value
    videoElement.value.muted = volume.value === 0
    isMuted.value = videoElement.value.muted
  }
}

// 播放速度控制
const handleSpeedChange = () => {
  if (videoElement.value) {
    videoElement.value.playbackRate = playbackRate.value
  }
}

// 全屏控制
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    playerContainer.value?.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 进度控制
const handleProgressClick = (event: MouseEvent) => {
  if (videoElement.value && duration.value > 0) {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const clickX = event.clientX - rect.left
    const clickRatio = clickX / rect.width
    const newTime = clickRatio * duration.value
    videoElement.value.currentTime = newTime
  }
}

// 控制条显示/隐藏
const showControlsTemporarily = () => {
  showControls.value = true
  if (controlsTimeout) {
    clearTimeout(controlsTimeout)
  }
  controlsTimeout = setTimeout(() => {
    if (isPlaying.value) {
      showControls.value = false
    }
  }, 3000)
}

const handleMouseMove = () => {
  showControlsTemporarily()
}

const hideControls = () => {
  if (isPlaying.value) {
    showControls.value = false
  }
}

// 事件处理
const handleLoadedMetadata = () => {
  if (videoElement.value) {
    duration.value = videoElement.value.duration
    volume.value = videoElement.value.volume
    isMuted.value = videoElement.value.muted

    emit('loadedmetadata', {
      width: videoElement.value.videoWidth,
      height: videoElement.value.videoHeight,
      duration: videoElement.value.duration
    })
    emit('ready', videoElement.value)
  }
}

const handlePlay = () => {
  isPlaying.value = true
  showCenterPlay.value = false
  showControlsTemporarily()
  emit('play')
}

const handlePause = () => {
  isPlaying.value = false
  showControls.value = true
  emit('pause')
}

const handleEnded = () => {
  isPlaying.value = false
  showCenterPlay.value = true
  showControls.value = true
  emit('ended')
}

const handleTimeUpdate = () => {
  if (videoElement.value && duration.value > 0) {
    currentTime.value = videoElement.value.currentTime
    playProgress.value = (currentTime.value / duration.value) * 100

    // 更新缓冲进度
    if (videoElement.value.buffered.length > 0) {
      const bufferedEnd = videoElement.value.buffered.end(videoElement.value.buffered.length - 1)
      bufferProgress.value = (bufferedEnd / duration.value) * 100
    }

    emit('timeupdate', currentTime.value)
  }
}

const handleVolumeChange = () => {
  if (videoElement.value) {
    volume.value = videoElement.value.volume
    isMuted.value = videoElement.value.muted
    emit('volumechange', volume.value)
  }
}

const handleError = (error: any) => {
  console.error('视频播放错误:', error)
  isLoading.value = false

  // 如果普通播放器失败，尝试iframe播放器
  if (!useIframePlayer.value && isProtectedPlatform(props.src)) {
    console.log('普通播放器失败，尝试iframe播放器')
    useIframePlayer.value = true
    iframeSrc.value = props.src
    processedSrc.value = ''
    return
  }

  emit('error', error)
}

// iframe加载完成处理
const handleIframeLoad = () => {
  console.log('iframe播放器加载完成')
  isLoading.value = false
  showCenterPlay.value = false

  // 对于iframe播放器，我们无法获取真实的视频尺寸
  // 使用默认尺寸或从URL推测
  emit('loadedmetadata', {
    width: 1920,
    height: 1080,
    duration: 0
  })

  if (iframeElement.value) {
    emit('ready', iframeElement.value as any)
  }
}

// 格式化时间
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

// 暴露方法
defineExpose({
  videoElement,
  play,
  pause,
  togglePlay,
  seek: (time: number) => {
    if (videoElement.value) {
      videoElement.value.currentTime = time
    }
  },
  setVolume: (vol: number) => {
    volume.value = vol
    handleVolumeSliderChange()
  },
  mute: () => {
    isMuted.value = true
    if (videoElement.value) {
      videoElement.value.muted = true
    }
  },
  unmute: () => {
    isMuted.value = false
    if (videoElement.value) {
      videoElement.value.muted = false
    }
  }
})

onMounted(async () => {
  // 首先处理视频源
  processVideoSource()

  await nextTick()

  if (!useIframePlayer.value && videoElement.value) {
    videoElement.value.muted = props.muted
    videoElement.value.loop = props.loop

    if (props.autoplay) {
      play()
    }
  }
})

// 监听src变化，重新处理视频源
watch(() => props.src, () => {
  processVideoSource()
})

onUnmounted(() => {
  if (controlsTimeout) {
    clearTimeout(controlsTimeout)
  }
})
</script>

<style scoped>
.modern-video-player {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  cursor: none;
}

.modern-video-player:hover {
  cursor: default;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: pointer;
}

.video-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 12px;
  background: #000;
}

/* 中央播放按钮 */
.center-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  cursor: pointer;
}

.play-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.7);
  border: 3px solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.play-icon:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: white;
  transform: scale(1.1);
}

/* 控制条 */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px 16px 16px;
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 20;
}

.controls-visible {
  opacity: 1;
  transform: translateY(0);
}

/* 进度条 */
.progress-container {
  margin-bottom: 12px;
  cursor: pointer;
}

.progress-track {
  position: relative;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-buffer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  transition: width 0.2s ease;
}

.progress-played {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #00d4aa;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background: #00d4aa;
  border: 2px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.progress-container:hover .progress-thumb {
  opacity: 1;
}

/* 控制按钮行 */
.controls-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.controls-left,
.controls-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: transparent;
  border: none;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 音量控制 */
.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-slider-container {
  width: 80px;
}

.volume-slider {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: #00d4aa;
  border-radius: 50%;
  cursor: pointer;
}

/* 时间显示 */
.time-display {
  color: white;
  font-size: 14px;
  font-weight: 500;
  min-width: 100px;
}

/* 速度选择 */
.speed-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.speed-select:focus {
  outline: none;
  border-color: #00d4aa;
}

/* 加载指示器 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls-row {
    flex-direction: column;
    gap: 8px;
  }

  .controls-left,
  .controls-right {
    width: 100%;
    justify-content: center;
  }

  .volume-slider-container {
    width: 60px;
  }

  .time-display {
    font-size: 12px;
    min-width: 80px;
  }
}

/* 暗色主题适配 */
.dark .modern-video-player {
  background: #1a1a1a;
}
</style>

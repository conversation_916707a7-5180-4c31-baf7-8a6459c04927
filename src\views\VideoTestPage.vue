<template>
  <div class="video-test-page">
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-3xl font-bold mb-8 text-center">视频播放器测试</h1>

      <!-- 测试视频URL输入 -->
      <div class="mb-8">
        <label class="block text-sm font-medium mb-2">测试视频URL：</label>
        <div class="flex gap-2">
          <input v-model="testVideoUrl" type="url" placeholder="输入视频链接进行测试..."
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          <button @click="loadTestVideo"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
            加载视频
          </button>
        </div>
      </div>

      <!-- 预设测试视频 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">预设测试视频：</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="(video, index) in testVideos" :key="index"
            class="border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            @click="loadVideo(video.url)">
            <h3 class="font-medium mb-2">{{ video.title }}</h3>
            <p class="text-sm text-gray-600 mb-2">{{ video.description }}</p>
            <p class="text-xs text-gray-500">{{ video.url }}</p>
          </div>
        </div>
      </div>

      <!-- 视频播放器展示 -->
      <div v-if="currentVideoUrl" class="mb-8">
        <h2 class="text-xl font-semibold mb-4">当前播放视频：</h2>
        <div class="bg-black rounded-lg overflow-hidden" style="aspect-ratio: 16/9;">
          <ModernVideoPlayer :src="currentVideoUrl" class="w-full h-full" @ready="handlePlayerReady"
            @loadedmetadata="handleLoadedMetadata" @error="handlePlayerError" @play="handlePlay" @pause="handlePause" />
        </div>

        <!-- 视频信息 -->
        <div v-if="videoInfo" class="mt-4 p-4 bg-gray-100 rounded-lg">
          <h3 class="font-medium mb-2">视频信息：</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="font-medium">宽度：</span>
              {{ videoInfo.width }}px
            </div>
            <div>
              <span class="font-medium">高度：</span>
              {{ videoInfo.height }}px
            </div>
            <div>
              <span class="font-medium">时长：</span>
              {{ formatDuration(videoInfo.duration) }}
            </div>
            <div>
              <span class="font-medium">状态：</span>
              {{ playbackStatus }}
            </div>
          </div>
        </div>
      </div>

      <!-- 视频缩略图测试 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">视频缩略图测试：</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <div v-for="(video, index) in testVideos" :key="`thumb-${index}`" class="aspect-video">
            <VideoThumbnail :src="video.url" @click="loadVideo(video.url)" @loadedmetadata="handleThumbnailLoaded"
              @error="handleThumbnailError" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ModernVideoPlayer from '@/components/video/ModernVideoPlayer.vue'
import VideoThumbnail from '@/components/video/VideoThumbnail.vue'

// 测试视频数据
const testVideos = [
  {
    title: 'Big Buck Bunny',
    description: '开源测试视频',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
  },
  {
    title: 'Elephant Dream',
    description: '开源短片',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
  },
  {
    title: 'For Bigger Blazes',
    description: '测试视频',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4'
  },
  {
    title: 'Sintel',
    description: 'Blender开源短片',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4'
  }
]

const testVideoUrl = ref('')
const currentVideoUrl = ref('')
const videoInfo = ref<{ width: number; height: number; duration: number } | null>(null)
const playbackStatus = ref('未播放')

// 加载测试视频
const loadTestVideo = () => {
  if (testVideoUrl.value.trim()) {
    loadVideo(testVideoUrl.value.trim())
  }
}

// 加载视频
const loadVideo = (url: string) => {
  currentVideoUrl.value = url
  videoInfo.value = null
  playbackStatus.value = '加载中...'
}

// 播放器就绪
const handlePlayerReady = (element: HTMLVideoElement) => {
  console.log('播放器就绪:', element)
  playbackStatus.value = '就绪'
}

// 视频元数据加载完成
const handleLoadedMetadata = (dimensions: { width: number; height: number; duration: number }) => {
  videoInfo.value = dimensions
  playbackStatus.value = '已加载'
  console.log('视频信息:', dimensions)
}

// 播放器错误
const handlePlayerError = (error: any) => {
  console.error('播放器错误:', error)
  playbackStatus.value = '播放错误'
}

// 开始播放
const handlePlay = () => {
  playbackStatus.value = '播放中'
}

// 暂停播放
const handlePause = () => {
  playbackStatus.value = '已暂停'
}

// 缩略图加载完成
const handleThumbnailLoaded = (data: any) => {
  console.log('缩略图加载完成:', data)
}

// 缩略图错误
const handleThumbnailError = (error: any) => {
  console.error('缩略图错误:', error)
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}
</script>

<style scoped>
.video-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.dark .video-test-page {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
}

.container {
  max-width: 1200px;
}

/* 暗色主题适配 */
.dark input {
  background: #374151;
  border-color: #4b5563;
  color: white;
}

.dark .border {
  border-color: #4b5563;
}

.dark .bg-gray-50:hover {
  background: #374151;
}

.dark .bg-gray-100 {
  background: #374151;
}

.dark .text-gray-600 {
  color: #9ca3af;
}

.dark .text-gray-500 {
  color: #6b7280;
}
</style>

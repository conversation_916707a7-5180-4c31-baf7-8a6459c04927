<template>
  <div ref="playerContainer" class="videojs-player-container">
    <!-- Video.js播放器 -->
    <video v-if="!useFallback" ref="videoElement" class="video-js vjs-default-skin" controls preload="auto"
      :poster="poster" data-setup="{}">
      <p class="vjs-no-js">
        要查看此视频，请启用JavaScript，并考虑升级到
        <a href="https://videojs.com/html5-video-support/" target="_blank">
          支持HTML5视频的Web浏览器
        </a>。
      </p>
    </video>

    <!-- 原生video fallback -->
    <video v-else ref="fallbackVideoElement" controls preload="auto" :poster="poster" :src="props.src"
      class="fallback-video" @loadedmetadata="handleFallbackLoadedMetadata" @error="handleFallbackError">
      您的浏览器不支持视频播放
    </video>

    <!-- 平台链接提示 -->
    <div v-if="showPlatformHint" class="platform-hint">
      <div class="hint-content">
        <div class="i-heroicons-information-circle w-6 h-6 text-blue-500 mb-2"></div>
        <p class="text-sm text-gray-700 mb-3">检测到平台视频链接</p>
        <button @click="openInNewTab" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          在新窗口中打开
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

import videojs from 'video.js'
import 'video.js/dist/video-js.css'

interface Props {
  src: string
  poster?: string
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
  fluid?: boolean
  responsive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: false,
  muted: false,
  loop: false,
  fluid: true,
  responsive: true,
})

interface Emits {
  ready: [player: any]
  play: []
  pause: []
  ended: []
  timeupdate: [currentTime: number]
  volumechange: [volume: number]
  error: [error: any]
  loadedmetadata: [dimensions: { width: number; height: number; duration: number }]
}

const emit = defineEmits<Emits>()

const playerContainer = ref<HTMLElement>()
const videoElement = ref<HTMLVideoElement>()
const fallbackVideoElement = ref<HTMLVideoElement>()
const showPlatformHint = ref(false)
const useFallback = ref(false)

let player: any = null

// 检测是否为平台视频链接
const isPlatformVideoUrl = (url: string): boolean => {
  const platformPatterns = [
    /douyin\.com\/aweme\/v\d+\/play/i,
    /kuaishou\.com\/short-video/i,
    /bilibili\.com\/video\//i,
    /v\.qq\.com\/x\/cover/i,
    /iqiyi\.com\/v_/i,
    /youku\.com\/v_show/i,
    /youtube\.com\/watch/i,
    /vimeo\.com\/\d+/i,
  ]

  return platformPatterns.some(pattern => pattern.test(url))
}

// 获取视频源配置
const getVideoSources = (url: string) => {
  // 如果是平台链接，显示提示而不是直接播放
  if (isPlatformVideoUrl(url)) {
    showPlatformHint.value = true
    return []
  }

  showPlatformHint.value = false

  // 直接视频文件
  return [{
    src: url,
    type: getVideoType(url)
  }]
}

// 获取视频类型
const getVideoType = (url: string): string => {
  const urlLower = url.toLowerCase()
  if (urlLower.includes('.mp4')) return 'video/mp4'
  if (urlLower.includes('.webm')) return 'video/webm'
  if (urlLower.includes('.ogg') || urlLower.includes('.ogv')) return 'video/ogg'
  if (urlLower.includes('.m3u8')) return 'application/x-mpegURL'
  if (urlLower.includes('.mpd')) return 'application/dash+xml'
  return 'video/mp4'
}

// 初始化播放器
const initPlayer = async () => {
  if (!videoElement.value) {
    console.error('VideoJsPlayer: videoElement not found')
    return
  }

  await nextTick()

  const sources = getVideoSources(props.src)
  console.log('VideoJsPlayer: 初始化播放器', { src: props.src, sources })

  // 如果是平台链接，不初始化播放器
  if (sources.length === 0) {
    console.log('VideoJsPlayer: 检测到平台链接，显示提示')
    return
  }

  try {
    // 简化的Video.js配置
    const options = {
      controls: true,
      fluid: props.fluid,
      responsive: props.responsive,
      autoplay: props.autoplay,
      muted: props.muted,
      loop: props.loop,
      preload: 'auto',
      sources: sources,
      poster: props.poster,
      playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2]
    }

    console.log('VideoJsPlayer: 创建播放器实例', options)

    // 创建播放器实例
    player = videojs(videoElement.value, options)

    // 绑定事件
    bindEvents()

    console.log('VideoJsPlayer: 播放器创建成功')
    emit('ready', player)
  } catch (error) {
    console.error('VideoJsPlayer: 初始化失败，使用原生video fallback', error)
    useFallback.value = true
    await nextTick()
    if (fallbackVideoElement.value) {
      emit('ready', fallbackVideoElement.value)
    }
  }
}

// Fallback video处理函数
const handleFallbackLoadedMetadata = () => {
  if (fallbackVideoElement.value) {
    const videoWidth = fallbackVideoElement.value.videoWidth || 1920
    const videoHeight = fallbackVideoElement.value.videoHeight || 1080
    const duration = fallbackVideoElement.value.duration || 0

    emit('loadedmetadata', {
      width: videoWidth,
      height: videoHeight,
      duration: duration
    })
  }
}

const handleFallbackError = (error: any) => {
  console.error('Fallback video error:', error)

  // 如果fallback也失败，检查是否是平台链接
  if (isPlatformVideoUrl(props.src)) {
    showPlatformHint.value = true
  } else {
    emit('error', error)
  }
}

// 绑定播放器事件
const bindEvents = () => {
  if (!player) return

  player.ready(() => {
    console.log('Video.js播放器就绪')
  })

  player.on('play', () => {
    emit('play')
  })

  player.on('pause', () => {
    emit('pause')
  })

  player.on('ended', () => {
    emit('ended')
  })

  player.on('timeupdate', () => {
    emit('timeupdate', player.currentTime())
  })

  player.on('volumechange', () => {
    emit('volumechange', player.volume())
  })

  player.on('loadedmetadata', () => {
    const videoWidth = player.videoWidth() || 1920
    const videoHeight = player.videoHeight() || 1080
    const duration = player.duration() || 0

    emit('loadedmetadata', {
      width: videoWidth,
      height: videoHeight,
      duration: duration
    })
  })

  player.on('error', (error: any) => {
    console.error('Video.js播放器错误:', error)

    // 如果是网络错误，可能是平台保护，显示提示
    const errorCode = player.error()?.code
    if (errorCode === 4 || errorCode === 2) { // MEDIA_ERR_SRC_NOT_SUPPORTED 或 MEDIA_ERR_NETWORK
      showPlatformHint.value = true
    }

    emit('error', error)
  })
}

// 销毁播放器
const destroyPlayer = () => {
  if (player) {
    player.dispose()
    player = null
  }
}

// 在新窗口打开
const openInNewTab = () => {
  window.open(props.src, '_blank')
}

// 监听src变化
watch(() => props.src, async (newSrc) => {
  if (newSrc) {
    destroyPlayer()
    await nextTick()
    initPlayer()
  }
})

// 暴露播放器方法
defineExpose({
  player: () => player,
  play: () => player?.play(),
  pause: () => player?.pause(),
  currentTime: (time?: number) => {
    if (time !== undefined) {
      player?.currentTime(time)
    }
    return player?.currentTime()
  },
  volume: (vol?: number) => {
    if (vol !== undefined) {
      player?.volume(vol)
    }
    return player?.volume()
  },
  muted: (mute?: boolean) => {
    if (mute !== undefined) {
      player?.muted(mute)
    }
    return player?.muted()
  },
  destroy: destroyPlayer
})

onMounted(() => {
  initPlayer()
})

onUnmounted(() => {
  destroyPlayer()
})
</script>

<style scoped>
.videojs-player-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Video.js自定义样式 */
:deep(.video-js) {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

:deep(.video-js .vjs-big-play-button) {
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.9);
  font-size: 2.5em;
  height: 80px;
  width: 80px;
  line-height: 76px;
  margin-top: -40px;
  margin-left: -40px;
  transition: all 0.3s ease;
}

:deep(.video-js .vjs-big-play-button:hover) {
  background: rgba(0, 0, 0, 0.9);
  border-color: white;
  transform: scale(1.1);
}

:deep(.video-js .vjs-control-bar) {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
}

:deep(.video-js .vjs-progress-control .vjs-progress-holder) {
  background: rgba(255, 255, 255, 0.3);
}

:deep(.video-js .vjs-progress-control .vjs-play-progress) {
  background: #00d4aa;
}

/* Fallback video样式 */
.fallback-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
  border-radius: 12px;
}

.platform-hint {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.hint-content {
  background: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  max-width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 暗色主题适配 */
.dark .hint-content {
  background: #1a1a1a;
  color: white;
}

.dark :deep(.video-js .vjs-big-play-button) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.8);
}

.dark :deep(.video-js .vjs-big-play-button:hover) {
  background: rgba(255, 255, 255, 0.2);
  border-color: white;
}
</style>

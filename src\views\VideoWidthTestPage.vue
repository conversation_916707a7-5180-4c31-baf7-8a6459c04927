<template>
  <div class="video-width-test-page">
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-3xl font-bold mb-8 text-center">视频宽度调整测试</h1>
      
      <!-- 测试不同宽高比的视频 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">不同宽高比的测试视频：</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="(video, index) in testVideos"
            :key="index"
            class="border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            @click="openVideoModal(video)"
          >
            <h3 class="font-medium mb-2">{{ video.title }}</h3>
            <p class="text-sm text-gray-600 mb-2">{{ video.description }}</p>
            <p class="text-xs text-gray-500 mb-2">宽高比: {{ video.aspectRatio }}</p>
            <p class="text-xs text-gray-400">{{ video.url }}</p>
          </div>
        </div>
      </div>

      <!-- 当前测试信息 -->
      <div v-if="currentVideo" class="mb-8 p-4 bg-blue-50 rounded-lg">
        <h3 class="font-medium mb-2">当前测试视频：</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span class="font-medium">标题：</span>
            {{ currentVideo.title }}
          </div>
          <div>
            <span class="font-medium">宽高比：</span>
            {{ currentVideo.aspectRatio }}
          </div>
          <div>
            <span class="font-medium">预期宽度：</span>
            {{ currentVideo.expectedWidth }}
          </div>
          <div>
            <span class="font-medium">预期高度：</span>
            {{ currentVideo.expectedHeight }}
          </div>
        </div>
      </div>

      <!-- 说明 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="font-medium mb-2">测试说明：</h3>
        <ul class="text-sm text-gray-600 space-y-1">
          <li>• 点击上方的测试视频卡片，会打开ImageDetailModal</li>
          <li>• 观察视频播放器的宽度是否根据视频的宽高比正确调整</li>
          <li>• 16:9的视频应该比较宽，9:16的视频应该比较窄</li>
          <li>• 1:1的视频应该是正方形</li>
          <li>• 模态窗口的总宽度应该根据视频宽高比动态调整</li>
        </ul>
      </div>
    </div>

    <!-- ImageDetailModal -->
    <ImageDetailModal
      v-if="showModal && selectedVideo"
      :image="selectedVideo"
      :visible="showModal"
      @close="closeModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ImageDetailModal from '@/components/image/ImageDetailModal.vue'

// 测试视频数据 - 不同宽高比
const testVideos = [
  {
    id: '1',
    title: '横屏视频 (16:9)',
    description: '标准横屏视频，宽屏格式',
    aspectRatio: '16:9',
    expectedWidth: '1920',
    expectedHeight: '1080',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    name: '横屏测试视频',
    originalName: 'BigBuckBunny.mp4',
    size: 15000000,
    type: 'video/mp4',
    uploadTime: new Date(),
    width: 1920,
    height: 1080,
    urls: [{
      hostId: 'external',
      hostName: '外部链接',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      uploadTime: new Date(),
      status: 'active' as const
    }],
    thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
  },
  {
    id: '2',
    title: '竖屏视频 (9:16)',
    description: '手机竖屏视频格式',
    aspectRatio: '9:16',
    expectedWidth: '720',
    expectedHeight: '1280',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    name: '竖屏测试视频',
    originalName: 'ElephantsDream.mp4',
    size: 12000000,
    type: 'video/mp4',
    uploadTime: new Date(),
    width: 720,
    height: 1280,
    urls: [{
      hostId: 'external',
      hostName: '外部链接',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
      uploadTime: new Date(),
      status: 'active' as const
    }],
    thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
  },
  {
    id: '3',
    title: '正方形视频 (1:1)',
    description: 'Instagram风格正方形视频',
    aspectRatio: '1:1',
    expectedWidth: '1080',
    expectedHeight: '1080',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    name: '正方形测试视频',
    originalName: 'ForBiggerBlazes.mp4',
    size: 10000000,
    type: 'video/mp4',
    uploadTime: new Date(),
    width: 1080,
    height: 1080,
    urls: [{
      hostId: 'external',
      hostName: '外部链接',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
      uploadTime: new Date(),
      status: 'active' as const
    }],
    thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4'
  },
  {
    id: '4',
    title: '超宽屏视频 (21:9)',
    description: '电影级超宽屏格式',
    aspectRatio: '21:9',
    expectedWidth: '2560',
    expectedHeight: '1080',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
    name: '超宽屏测试视频',
    originalName: 'Sintel.mp4',
    size: 18000000,
    type: 'video/mp4',
    uploadTime: new Date(),
    width: 2560,
    height: 1080,
    urls: [{
      hostId: 'external',
      hostName: '外部链接',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
      uploadTime: new Date(),
      status: 'active' as const
    }],
    thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4'
  },
  {
    id: '5',
    title: '标准4:3视频',
    description: '传统4:3格式视频',
    aspectRatio: '4:3',
    expectedWidth: '1024',
    expectedHeight: '768',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    name: '4:3测试视频',
    originalName: 'BigBuckBunny_4_3.mp4',
    size: 14000000,
    type: 'video/mp4',
    uploadTime: new Date(),
    width: 1024,
    height: 768,
    urls: [{
      hostId: 'external',
      hostName: '外部链接',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      uploadTime: new Date(),
      status: 'active' as const
    }],
    thumbnail: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
  }
]

const currentVideo = ref<any>(null)
const selectedVideo = ref<any>(null)
const showModal = ref(false)

// 打开视频模态框
const openVideoModal = (video: any) => {
  currentVideo.value = video
  selectedVideo.value = video
  showModal.value = true
  console.log('打开视频模态框:', video.title, `尺寸: ${video.width}x${video.height}`)
}

// 关闭模态框
const closeModal = () => {
  showModal.value = false
  selectedVideo.value = null
  console.log('关闭视频模态框')
}
</script>

<style scoped>
.video-width-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.dark .video-width-test-page {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
}

.container {
  max-width: 1200px;
}

/* 暗色主题适配 */
.dark .border {
  border-color: #4b5563;
}

.dark .bg-gray-50:hover {
  background: #374151;
}

.dark .bg-gray-50 {
  background: #374151;
}

.dark .bg-blue-50 {
  background: #1e3a8a;
}

.dark .text-gray-600 {
  color: #9ca3af;
}

.dark .text-gray-500 {
  color: #6b7280;
}

.dark .text-gray-400 {
  color: #9ca3af;
}
</style>

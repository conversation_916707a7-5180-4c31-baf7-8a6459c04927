<template>
  <!-- 使用 vue-easy-lightbox 实现Instagram风格的图片预览 -->
  <vue-easy-lightbox
    :visible="visible"
    :imgs="imageUrls"
    :index="currentIndex"
    @hide="handleClose"
    :loop="false"
    :zoom-disabled="false"
    :move-disabled="false"
    :rotate-disabled="true"
    :mask-closable="true"
    :scroll-disabled="true"
    :teleport="true"
    :dblclick-disabled="false"
  >
    <!-- 自定义工具栏 -->
    <template #toolbar="{ toolbarMethods }">
      <div class="instagram-toolbar">
        <!-- 左侧：图片信息 -->
        <div class="toolbar-left">
          <div class="image-info">
            <div class="text-sm text-white">
              {{ currentImage?.originalName }}
            </div>
            <div class="text-xs text-gray-300">
              {{ formatImageSize(currentImage) }} • {{ formatFileSize(currentImage?.size || 0) }}
            </div>
          </div>
        </div>
        
        <!-- 右侧：操作按钮 -->
        <div class="toolbar-right">
          <button @click="toolbarMethods.zoomIn" class="toolbar-btn" title="放大">
            <div class="i-heroicons-plus w-5 h-5"></div>
          </button>
          <button @click="toolbarMethods.zoomOut" class="toolbar-btn" title="缩小">
            <div class="i-heroicons-minus w-5 h-5"></div>
          </button>
          <button @click="toolbarMethods.reset" class="toolbar-btn" title="重置">
            <div class="i-heroicons-arrow-path w-5 h-5"></div>
          </button>
          <button @click="downloadImage" class="toolbar-btn" title="下载">
            <div class="i-heroicons-arrow-down-tray w-5 h-5"></div>
          </button>
          <button @click="copyImageUrl" class="toolbar-btn" title="复制链接">
            <div class="i-heroicons-clipboard w-5 h-5"></div>
          </button>
          <button @click="handleClose" class="toolbar-btn" title="关闭">
            <div class="i-heroicons-x-mark w-5 h-5"></div>
          </button>
        </div>
      </div>
    </template>
    
    <!-- 自定义侧边栏 - Instagram风格的信息面板 -->
    <template #sidebar>
      <div class="instagram-sidebar">
        <div class="sidebar-header">
          <h3 class="text-lg font-semibold text-white mb-4">图片详情</h3>
        </div>
        
        <div class="sidebar-content">
          <!-- 基本信息 -->
          <div class="info-section">
            <h4 class="section-title">基本信息</h4>
            <div class="info-item">
              <span class="info-label">文件名</span>
              <span class="info-value">{{ currentImage?.originalName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">尺寸</span>
              <span class="info-value">{{ formatImageSize(currentImage) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">大小</span>
              <span class="info-value">{{ formatFileSize(currentImage?.size || 0) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">上传时间</span>
              <span class="info-value">{{ formatDate(currentImage?.uploadTime) }}</span>
            </div>
          </div>
          
          <!-- 备份链接 -->
          <div class="info-section">
            <h4 class="section-title">备份链接</h4>
            <div v-if="currentImage?.urls?.length" class="urls-list">
              <div v-for="(urlRecord, index) in currentImage.urls" :key="index" class="url-item">
                <div class="url-header">
                  <span class="url-host">{{ urlRecord.hostName }}</span>
                  <span class="url-status" :class="getStatusClass(urlRecord.status)">
                    {{ getStatusText(urlRecord.status) }}
                  </span>
                </div>
                <div class="url-actions">
                  <button @click="copyUrl(urlRecord.url)" class="action-btn">
                    <div class="i-heroicons-clipboard w-4 h-4"></div>
                  </button>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <p class="text-gray-400 text-sm">暂无备份链接</p>
            </div>
          </div>
          
          <!-- 标签 -->
          <div class="info-section">
            <h4 class="section-title">标签</h4>
            <div v-if="currentImage?.tags?.length" class="tags-list">
              <span v-for="tag in currentImage.tags" :key="tag" class="tag">
                {{ tag }}
              </span>
            </div>
            <div v-else class="empty-state">
              <p class="text-gray-400 text-sm">暂无标签</p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </vue-easy-lightbox>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
// import VueEasyLightbox from 'vue-easy-lightbox'

// 类型定义
interface ImageRecord {
  id: string
  originalName: string
  size: number
  width?: number
  height?: number
  type: string
  uploadTime: Date
  tags: string[]
  urls: ImageUrlRecord[]
}

interface ImageUrlRecord {
  id?: string
  hostId: string
  hostName: string
  url: string
  deleteUrl?: string
  uploadTime: Date
  status: 'active' | 'failed' | 'unknown'
}

interface Props {
  images: ImageRecord[]
  currentIndex: number
  visible: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  edit: [image: ImageRecord]
  delete: [imageId: string]
}>()

// 计算属性
const imageUrls = computed(() => {
  return props.images.map(image => {
    const activeUrl = image.urls.find(url => url.status === 'active')
    return activeUrl?.url || image.urls[0]?.url || ''
  })
})

const currentImage = computed(() => {
  return props.images[props.currentIndex]
})

// 方法
const handleClose = () => {
  emit('close')
}

const formatImageSize = (image?: ImageRecord) => {
  if (!image || (!image.width && !image.height)) return '未知'
  return `${image.width || 0} × ${image.height || 0}`
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date?: Date): string => {
  if (!date) return '未知'
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'active': return 'status-active'
    case 'failed': return 'status-failed'
    default: return 'status-unknown'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '正常'
    case 'failed': return '失效'
    default: return '未知'
  }
}

const copyUrl = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    message.success('链接复制成功')
  } catch (err) {
    message.error('复制失败')
  }
}

const copyImageUrl = async () => {
  const url = imageUrls.value[props.currentIndex]
  if (url) {
    await copyUrl(url)
  }
}

const downloadImage = () => {
  const url = imageUrls.value[props.currentIndex]
  const image = currentImage.value
  if (url && image) {
    const link = document.createElement('a')
    link.href = url
    link.download = image.originalName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}
</script>

<style scoped>
/* Instagram风格工具栏 */
.instagram-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.toolbar-left .image-info {
  color: white;
}

.toolbar-right {
  display: flex;
  gap: 0.5rem;
}

.toolbar-btn {
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 0.375rem;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* Instagram风格侧边栏 */
.instagram-sidebar {
  width: 400px;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  padding: 2rem;
  overflow-y: auto;
  color: white;
}

.sidebar-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.info-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.info-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.info-value {
  color: white;
  font-size: 0.875rem;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.urls-list {
  space-y: 0.5rem;
}

.url-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
}

.url-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.url-host {
  font-weight: 500;
  color: white;
}

.url-status {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.status-active {
  background: rgba(34, 197, 94, 0.2);
  color: rgb(34, 197, 94);
}

.status-failed {
  background: rgba(239, 68, 68, 0.2);
  color: rgb(239, 68, 68);
}

.status-unknown {
  background: rgba(156, 163, 175, 0.2);
  color: rgb(156, 163, 175);
}

.url-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.25rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 0.25rem;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: rgba(59, 130, 246, 0.2);
  color: rgb(59, 130, 246);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
}

.empty-state {
  text-align: center;
  padding: 2rem 0;
}
</style>

<template>
  <div class="video-thumbnail-container">
    <video
      ref="videoElement"
      :src="src"
      preload="metadata"
      muted
      playsinline
      class="video-thumbnail"
      @loadedmetadata="handleLoadedMetadata"
      @error="handleError"
      @click="handleClick"
    >
      您的浏览器不支持视频播放
    </video>
    
    <!-- 播放图标覆盖层 -->
    <div class="video-play-overlay" @click="handleClick">
      <div class="play-icon">
        <div class="i-heroicons-play-circle w-8 h-8 text-white opacity-90"></div>
      </div>
    </div>
    
    <!-- 视频时长显示 -->
    <div v-if="duration" class="video-duration">
      {{ formatDuration(duration) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Props {
  src: string
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  clickable: true
})

interface Emits {
  click: []
  loadedmetadata: [dimensions: { width: number; height: number; duration: number }]
  error: [error: any]
}

const emit = defineEmits<Emits>()

const videoElement = ref<HTMLVideoElement>()
const duration = ref<number>(0)

// 处理视频元数据加载
const handleLoadedMetadata = () => {
  if (videoElement.value) {
    duration.value = videoElement.value.duration
    emit('loadedmetadata', {
      width: videoElement.value.videoWidth,
      height: videoElement.value.videoHeight,
      duration: videoElement.value.duration
    })
  }
}

// 处理播放错误
const handleError = (error: any) => {
  console.error('视频缩略图加载错误:', error)
  emit('error', error)
}

// 处理点击事件
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}

// 格式化时长显示
const formatDuration = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

onMounted(() => {
  // 尝试获取视频第一帧作为缩略图
  if (videoElement.value) {
    videoElement.value.currentTime = 1 // 跳到第1秒获取缩略图
  }
})
</script>

<style scoped>
.video-thumbnail-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  background: #000;
  cursor: pointer;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.video-thumbnail-container:hover .video-thumbnail {
  transform: scale(1.05);
}

.video-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.video-thumbnail-container:hover .video-play-overlay {
  opacity: 1;
  pointer-events: auto;
}

.play-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;
}

.play-icon:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(4px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .play-icon {
    width: 48px;
    height: 48px;
  }
  
  .play-icon .i-heroicons-play-circle {
    width: 6rem;
    height: 6rem;
  }
  
  .video-duration {
    font-size: 11px;
    padding: 1px 4px;
  }
}

/* 暗色主题适配 */
.dark .video-thumbnail-container {
  background: #1a1a1a;
}

.dark .video-play-overlay {
  background: rgba(0, 0, 0, 0.5);
}

.dark .play-icon {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .play-icon:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dark .video-duration {
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>

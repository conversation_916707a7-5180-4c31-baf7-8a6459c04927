<template>
  <div class="image-gallery">
    <!-- 图片网格 -->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      <div
        v-for="(image, index) in images"
        :key="image.id"
        class="gallery-item group cursor-pointer relative overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-800 aspect-square"
        @click="openLightbox(index)"
      >
        <!-- 缩略图 -->
        <img
          :src="getThumbnailUrl(image)"
          :alt="image.originalName"
          class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
          loading="lazy"
        />
        
        <!-- 悬浮信息 -->
        <div class="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors duration-300 flex items-end">
          <div class="p-3 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div class="text-sm font-medium truncate">{{ image.originalName }}</div>
            <div class="text-xs text-gray-300">{{ formatFileSize(image.size) }}</div>
          </div>
        </div>
        
        <!-- 状态指示器 -->
        <div class="absolute top-2 right-2">
          <div
            class="w-3 h-3 rounded-full"
            :class="getStatusIndicatorClass(image)"
            :title="getStatusText(image)"
          ></div>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="images.length === 0" class="empty-state text-center py-12">
      <div class="i-heroicons-photo w-16 h-16 mx-auto text-gray-400 mb-4"></div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">暂无图片</h3>
      <p class="text-gray-500 dark:text-gray-400">上传一些图片来开始使用吧</p>
    </div>

    <!-- Instagram风格的图片预览 -->
    <vue-easy-lightbox
      :visible="lightboxVisible"
      :imgs="lightboxImages"
      :index="currentIndex"
      @hide="closeLightbox"
      :loop="true"
      :zoom-disabled="false"
      :move-disabled="false"
      :rotate-disabled="true"
      :mask-closable="true"
      :scroll-disabled="true"
      :teleport="true"
      :dblclick-disabled="false"
      :max-zoom="5"
      :min-zoom="0.1"
    >
      <!-- 自定义工具栏 -->
      <template #toolbar="{ toolbarMethods, imgIndex }">
        <div class="instagram-toolbar">
          <!-- 左侧：图片信息 -->
          <div class="toolbar-left">
            <div class="image-info">
              <div class="text-sm font-medium text-white">
                {{ currentImage?.originalName }}
              </div>
              <div class="text-xs text-gray-300">
                {{ formatImageSize(currentImage) }} • {{ formatFileSize(currentImage?.size || 0) }}
              </div>
            </div>
          </div>
          
          <!-- 中间：页码指示器 -->
          <div class="toolbar-center">
            <div class="text-sm text-white">
              {{ imgIndex + 1 }} / {{ images.length }}
            </div>
          </div>
          
          <!-- 右侧：操作按钮 -->
          <div class="toolbar-right">
            <button @click="toolbarMethods.zoomIn" class="toolbar-btn" title="放大">
              <div class="i-heroicons-plus w-5 h-5"></div>
            </button>
            <button @click="toolbarMethods.zoomOut" class="toolbar-btn" title="缩小">
              <div class="i-heroicons-minus w-5 h-5"></div>
            </button>
            <button @click="toolbarMethods.reset" class="toolbar-btn" title="重置">
              <div class="i-heroicons-arrow-path w-5 h-5"></div>
            </button>
            <button @click="downloadCurrentImage" class="toolbar-btn" title="下载">
              <div class="i-heroicons-arrow-down-tray w-5 h-5"></div>
            </button>
            <button @click="copyCurrentImageUrl" class="toolbar-btn" title="复制链接">
              <div class="i-heroicons-clipboard w-5 h-5"></div>
            </button>
            <button @click="editCurrentImage" class="toolbar-btn" title="编辑">
              <div class="i-heroicons-pencil w-5 h-5"></div>
            </button>
            <button @click="closeLightbox" class="toolbar-btn" title="关闭">
              <div class="i-heroicons-x-mark w-5 h-5"></div>
            </button>
          </div>
        </div>
      </template>
    </vue-easy-lightbox>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
// import VueEasyLightbox from 'vue-easy-lightbox'

// 类型定义
interface ImageRecord {
  id: string
  originalName: string
  size: number
  width?: number
  height?: number
  type: string
  uploadTime: Date
  tags: string[]
  urls: ImageUrlRecord[]
}

interface ImageUrlRecord {
  id?: string
  hostId: string
  hostName: string
  url: string
  deleteUrl?: string
  uploadTime: Date
  status: 'active' | 'failed' | 'unknown'
}

interface Props {
  images: ImageRecord[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  edit: [image: ImageRecord]
  delete: [imageId: string]
}>()

// 响应式数据
const lightboxVisible = ref(false)
const currentIndex = ref(0)

// 计算属性
const lightboxImages = computed(() => {
  return props.images.map(image => {
    const activeUrl = image.urls.find(url => url.status === 'active')
    return {
      src: activeUrl?.url || image.urls[0]?.url || '',
      title: image.originalName
    }
  })
})

const currentImage = computed(() => {
  return props.images[currentIndex.value]
})

// 方法
const openLightbox = (index: number) => {
  currentIndex.value = index
  lightboxVisible.value = true
}

const closeLightbox = () => {
  lightboxVisible.value = false
}

const getThumbnailUrl = (image: ImageRecord): string => {
  const activeUrl = image.urls.find(url => url.status === 'active')
  return activeUrl?.url || image.urls[0]?.url || ''
}

const getStatusIndicatorClass = (image: ImageRecord): string => {
  const hasActiveUrls = image.urls.some(url => url.status === 'active')
  return hasActiveUrls 
    ? 'bg-green-500' 
    : 'bg-red-500'
}

const getStatusText = (image: ImageRecord): string => {
  const hasActiveUrls = image.urls.some(url => url.status === 'active')
  return hasActiveUrls ? '链接正常' : '链接失效'
}

const formatImageSize = (image?: ImageRecord): string => {
  if (!image || (!image.width && !image.height)) return '未知尺寸'
  return `${image.width || 0} × ${image.height || 0}`
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const downloadCurrentImage = () => {
  const image = currentImage.value
  const url = getThumbnailUrl(image)
  
  if (url && image) {
    const link = document.createElement('a')
    link.href = url
    link.download = image.originalName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    message.success('开始下载')
  }
}

const copyCurrentImageUrl = async () => {
  const url = getThumbnailUrl(currentImage.value)
  if (url) {
    try {
      await navigator.clipboard.writeText(url)
      message.success('链接复制成功')
    } catch (err) {
      message.error('复制失败')
    }
  }
}

const editCurrentImage = () => {
  const image = currentImage.value
  if (image) {
    emit('edit', image)
    closeLightbox()
  }
}
</script>

<style scoped>
/* 画廊网格样式 */
.gallery-item {
  transition: all 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Instagram风格工具栏 */
.instagram-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.toolbar-left .image-info {
  color: white;
}

.toolbar-center {
  color: white;
  font-weight: 500;
}

.toolbar-right {
  display: flex;
  gap: 0.5rem;
}

.toolbar-btn {
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 0.375rem;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.toolbar-btn:active {
  transform: scale(0.95);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .instagram-toolbar {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .toolbar-right {
    justify-content: space-around;
  }
}
</style>
